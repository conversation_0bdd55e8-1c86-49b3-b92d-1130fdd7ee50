

import loadCloudFontFace from '@/utils/loadFontHelper'

export const downLoadAlimamaShuHeiTiFont = () => {
	// 阿里妈妈数黑体
	const url = 'https://www.duelchannel.com/min-api/file/resource/AlimamaShuHeiTi-Bold.woff'
	const fileName = 'AlimamaShuHeiTi.woff'
	const fontFamily = 'AlimamaShuHeiTi'
	return loadCloudFontFace(url, fileName, fontFamily)
}
export const downLoadYouSheBiaoTiHeiFont = () => {
	// 优设标题黑
	const url = 'https://www.duelchannel.com/min-api/file/resource/title.woff'
	const fileName = 'YouSheBiaoTiHei.woff'
	const fontFamily = 'YouSheBiaoTiHei'
	return loadCloudFontFace(url, fileName, fontFamily)

}
export const downLoadDeYiHeiFont = () => {

	// 得意黑
	const url = 'https://www.duelchannel.com/min-api/file/resource/SmileySans-Oblique.woff'
	const fileName = 'DeYiHei.woff'
	const fontFamily = 'DeYiHei'
	return loadCloudFontFace(url, fileName, fontFamily)
}

export const downLoadBerkshireSwashFont = () => {
	// LV等级后面的数字用
	const url = 'https://www.duelchannel.com/min-api/file/resource/Berkshire-Swash.woff'
	const fileName = 'BerkshireSwash.woff'
	const fontFamily = 'BerkshireSwash'
	return loadCloudFontFace(url, fileName, fontFamily)
}
export const downLoadPassionOneFont = () => {
	// LV英文字体用
	const url = 'https://www.duelchannel.com/min-api/file/resource/Passion-One.woff'
	const fileName = 'PassionOne.woff'
	const fontFamily = 'PassionOne'
	return loadCloudFontFace(url, fileName, fontFamily)
}



// export const downLoadAlimamaShuHeiTiFont = () => {
// 	// 阿里妈妈数黑体
// 	const url = 'https://www.duelchannel.com/min-api/file/resource/AlimamaShuHeiTi-Bold.woff'

// 	return uni.loadFontFace({
// 		global: true,
// 		family: 'AlimamaShuHeiTi',
// 		source: `url("${url}")`,
// 		scopes: ["webview", "native"],
// 	})
// }
// export const downLoadYouSheBiaoTiHeiFont = () => {
// 	// 优设标题黑
// 	const url = 'https://www.duelchannel.com/min-api/file/resource/title.woff'

// 	return uni.loadFontFace({
// 		global: true,
// 		family: 'YouSheBiaoTiHei',
// 		source: `url("${url}")`,
// 		scopes: ["webview", "native"],
// 	})
// }
// export const downLoadDeYiHeiFont = () => {

// 	// 得意黑
// 	const url = 'https://www.duelchannel.com/min-api/file/resource/SmileySans-Oblique.woff'

// 	return uni.loadFontFace({
// 		global: true,
// 		family: 'DeYiHei',
// 		source: `url("${url}")`,
// 		scopes: ["webview", "native"],
// 	})
// }

// export const downLoadBerkshireSwashFont = () => {
// 	// LV等级后面的数字用
// 	const url = 'https://www.duelchannel.com/min-api/file/resource/Berkshire-Swash.woff'

// 	return uni.loadFontFace({
// 		global: true,
// 		family: 'BerkshireSwash',
// 		source: `url("${url}")`,
// 		scopes: ["webview", "native"]
// 	})
// }
// export const downLoadPassionOneFont = () => {
// 	// LV英文字体用
// 	const url = 'https://www.duelchannel.com/min-api/file/resource/Passion-One.woff'

// 	return uni.loadFontFace({
// 		global: true,
// 		family: 'PassionOne',
// 		source: `url("${url}")`,
// 		scopes: ["webview", "native"],
// 	})
// }