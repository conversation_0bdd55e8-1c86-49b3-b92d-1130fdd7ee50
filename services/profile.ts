import type { ProfileDetail, MultipleOnlineVO } from '@/types/member.d.ts'
import http from '@/utils/http'

// 定义更新用户参数类型
type UpdateUserParams = {
	nickName ?: string,
	personalSignature ?: string,
	gameTypeId ?: string,
	gameId ?: string,
}

/**
 * 获取个人信息
 */
export const getMemberProfileAPI = () => {
	return http.get<ProfileDetail>('/player/mini/get')
}

/**
 * 获取用户多端在线详情
 */
export const getMemberMultipleOnlineAPI = (id : string) => {
	return http.post<MultipleOnlineVO>('/player/mini/multipleOnline', { id })
}

/**
 * 更新个人信息
 */
export const updateMemberProfileAPI = (data : UpdateUserParams) => {
	return http.put<object>('/player/mini/update', data)
}

/**
 * 更新用户在线状态
 */
export const updateMemberOnlineAPI = (online : string) => {
	return http.put<object>('/player/mini/online', { online })
}

// 通过uid 获取用户信息
export const getMemberProfileByUidAPI = (id : string) => {
	return http.put<ProfileDetail>('/player/mini/getByUid', { id })
}


type UploadMemberAvatarParams = {
	name : string,
	filePath : string
}
type UploadMemberAvatarRes = {
	imgUrl ?: string
}
/**
 * 上传用户头像
 */
export const uploadMemberAvatarAPI = (config : UploadMemberAvatarParams) => {
	return http.upload<UploadMemberAvatarRes>('/player/mini/upload-avatar', { name: config.name, filePath: config.filePath })
}