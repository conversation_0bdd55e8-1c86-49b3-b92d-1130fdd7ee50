import type { FriendItem } from '@/types/friend.d.ts'
import http from '@/utils/http'


// 定义类型
type GetFriendListParams = {
	friendUid ?: string,
	pageNum ?: number,
	pageSize ?: number,
	orderByColumn ?: string,
	isAsc ?: string,
}

/**
 * 获取好友列表
 */
export const getFriendListAPI = (data : GetFriendListParams) => {
	return http.getPaginationList<FriendItem[]>('/friend/mini/page', data)
}
/**
 * 添加好友
 */
export const addFriendAPI = (friendUid : string) => {
	let data = { friendUid };
	return http.post<object>('/friend/mini/addFriend', data)
}
/**
 * 删除好友
 */
export const deleteFriendAPI = (friendUid : string) => {
	let data = { friendUid };
	return http.delete<object>('/friend/mini/delete', data)
}
/**
 * 拉黑好友
 */
export const maskingFriendAPI = (friendUid : string) => {
	let data = { friendUid };
	return http.delete<object>('/friend/mini/black', data)
}
/**
 * 同意或者拒绝好友
 */
export const agreeOrRefuseFriendApi = (friendUid : string, messageId : string, operate : string) => {
	let data = { friendUid, messageId, operate };
	return http.post<object>('/friend/mini/applyHandle', data)
}