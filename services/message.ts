import type { MessageListResult, FriendApplyItem } from '@/types/message.d.ts'
import http from '@/utils/http'

// 定义类型
type MessageListParams = {
	//消息状态（消息状态(1未读,2已读) 不传为全部）
	status ?: number,
}

/**
 * 获取好友申请消息
 */
export const getApplyAddFriendAPI = () => {
	return http.post<FriendApplyItem[]>('/player/mini/getApplyAddFriend')
}

/**
 * 获取混合消息列表
 */
export const getMessageListAPI = (data : MessageListParams) => {
	return http.post<MessageListResult>('/site-message/mini/getMultipleMessageCount', data)
}
/**
 * 设置已读消息
 */
export const setReadMessageAPI = (ids : number[]) => {
	const data = { ids }
	return http.post<object>('/site-message/mini/readMessage', data)
}

/**
 * 站内信已处理
 */
export const operateMessageAPI = (ids : number[]) => {
	const data = { ids }
	return http.post<object>('/site-message/mini/operateMessage', data)
}

/**
 * 发送站内信
 */
export const sendMessageAPI = (toUid : string, message : string) => {
	const data = { toUid, message }
	return http.post<object>('/site-message/mini/create', data)
}

/**
 * 发送反馈消息
 */
export const sendFeedbackAPI = (title : string, message : string) => {
	const data = { title, message }
	return http.post<object>('/feedback/mini/create', data)
}