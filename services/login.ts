import http from '@/utils/http'
import { LoginResult } from "@/types/member.d.ts"
import { ErrorResultData, TcgHttpError } from '@/utils/http';
import tcgChat from '@/utils/TcgIMHelper'


export const wxLogin = () => {
	return new Promise<string>((resolve, reject) => {
		wx.login({
			success(res) {
				resolve(res.code);
			},
			fail(err) {
				let errorResultData : ErrorResultData = {
					msg: err.errMsg,
					url: 'wxLogin',
					code: 500
				}
				reject(new TcgHttpError(errorResultData));
			}
		});
	});
};
// 定义类型
type LoginIMParams = {
	userID : string,
	userSig : string
}
export const loginIM = (data : LoginIMParams) => {
	return new Promise<any>((resolve, reject) => {
		tcgChat.login({ userID: data.userID, userSig: data.userSig }).then((res) => {
			resolve(res)
		}).catch((error) => {
			// 打印error类型
			console.log('登录IM失败', error)
			let errorResultData : ErrorResultData = {
				msg: '登录IM失败',
				url: 'loginIM',
				code: 500
			}
			reject(new TcgHttpError(errorResultData))
		});
	});
};

// 定义类型
type LoginParams = {
	loginCode : string,
	phoneCode ?: string
}
// 小程序登录

export const postLoginWxMinAPI = (data : LoginParams) => {
	return http.post<LoginResult>('/auth/mini-login', data)
}

// PC扫码登录

// 定义类型
type ScanQRCodeParams = {
	uuid : string
}
export const scanQRCodeAPI = (data : ScanQRCodeParams) => {
	return http.post<object>('/auth/mini-uuid', data)
}