import http from '@/utils/http'
import type { RoomListItem, RoomDetail, RoomUserItem, RoomOnlineVo } from '@/types/room.d.ts';

// 定义更新用户参数类型
type UndateRoomParams = {
	rid : string,
	type : number,
	gameTypeId : string,
	gameId : string,
	watchType : number,
}
type CreateRoomParams = {
	type : number,
	gameTypeId : string,
	gameId : string,
	watchType : number,
}
type MatchRoomParams = {
	gameTypeId : string,
	gameId : string,
}

/**
 * 获取邀请列表
 */
// export const getInviteMessageAPI = () => {
// 	return http.get<RoomListItem[]>('/room/mini/hallRoom')
// }
export const getInviteMessageAPI = (data : MatchRoomParams) => {
	return http.post<RoomListItem[]>('/room/mini/hallRoomBy', data)
}

/**
 * 创建房间
 * type	房间类型：1私密 2公开,示例值(1)
 * gameTypeId	游戏赛制类型：1普通,示例值(2)
 * gameId	游戏编号,示例值(2)
 * watchType	观战：1开启观战，2关闭观战,示例值(2)
 */
export const createRoomAPI = (data : CreateRoomParams) => {
	return http.post<RoomDetail>('/room/mini/create', data)
}

/**
 * 邀请加入房间
 * rid	房间id
 * playerUid	被邀请人uid
 */
export const inviteJoinRoomAPI = (rid : string, playerUid : string) => {
	return http.post<object>('/room/user/mini/invite', { rid, playerUid })
}



/**
 * 加入房间
 */
export const joinRoomAPI = (rid : string, joinType : number, userType : number = 1) => {
	return http.post<RoomDetail>('/room/mini/joinRoom', { rid, joinType, userType })
}


/**
 * 匹配房间
 */
export const matchRoomAPI = (data : MatchRoomParams) => {
	return http.post<RoomDetail>('/room/mini/matchRoom', data)
}
/**
 * 退出房间
 */
export const leaveRoomAPI = (rid : string) => {
	return http.post<RoomDetail>('/room/mini/outRoom', { rid })
}

/**
 * 请离对手
 */
export const pleaseLeaveRoomAPI = (rid : string, playerUid : string) => {
	return http.post<object>('/room/user/mini/deleteUser', { rid, playerUid })
}



/**
 * 更新房间信息
 */
export const updateRoomAPI = (data : UndateRoomParams) => {
	return http.put<RoomDetail>('/room/mini/update', data)
}

/**
 * 获取房间信息
 */
export const getRoomDetailAPI = (rid : string) => {
	return http.get<RoomDetail>('/room/mini/getByRid', { rid })
}

/**
 * 获取房间成员列表
 */
export const getRoomUserListAPI = (rid : string) => {
	return http.get<RoomUserItem[]>('/room/user/mini/userList', { rid })
}

/**
 * 获取房间成员详情
 */
export const getRoomUserDetailAPI = (rid : string, uid : string) => {
	return http.get<RoomUserItem>('/room/user/mini/user', { rid, uid })
}

/**
 * 获取玩家目前在线房间
 */
export const getUserOnlineRoomAPI = () => {
	return http.get<RoomDetail>('/room/mini/getRoom')
}

/**
 * 续期房间userSig
 */
// export const renewalRoomUserSigAPI = (roomId : string) => {
// 	return http.post<number>('/player/mini/extend-status', { roomId })
// }

type GiveLikeParams = {
	// 被点赞人的uid
	receiverUid : string,
	// 业务类型(1点赞，2签到，3胜利)
	bizType : number,
	// 房间号
	rid ?: string,
}
/**
 * 给好友点赞api
 */
export const giveLikeAPI = (data : GiveLikeParams) => {
	return http.put<number>('/point-record/mini/liked', data)
}

type RoomReadyParams = {
	// 房间号
	rid : string,
	// 玩家uid
	playerUid : string,
	// 准备状态(0已准备 1未准备)	
	ready : number,
}

/**
 * 玩家准备
 */
export const roomPlayerReadyAPI = (data : RoomReadyParams) => {
	return http.post<object>('/room/user/mini/readyUser', data)
}


/**
 * 创建会议室
 */
export const createTVRoomAPI = (rid : string, tvGroupId : string) => {
	return http.post<object>('/room/tv/mini/createTV', { rid, tvGroupId })
}

/**
 * 进入会议室
 */
export const enterTVRoomAPI = (rid : string) => {
	return http.post<object>('/room/tv/mini/enterTV', { rid })
}

export const leaveTVRoomAPI = (rid : string) => {
	return http.post<object>('/room/tv/mini/outTV', { rid })
}

/**
 * 获取房间用户多端在线详情
 */
export const getRoomMultipleOnlineAPI = (rid : string) => {
	return http.post<RoomOnlineVo>('/room/tv/mini/roomMultipleOnline', { rid })
}