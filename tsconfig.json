// tsconfig.json
{
	"compilerOptions": {
		"allowJs": true,
		"target": "esnext",
		"module": "esnext",
		"strict": false,
		"jsx": "preserve",
		"moduleResolution": "node",
		"esModuleInterop": true,
		"sourceMap": true,
		"skipLibCheck": true,
		"importHelpers": true,
		"allowSyntheticDefaultImports": true,
		"useDefineForClassFields": true,
		"resolveJsonModule": true,
		"lib": [
			"esnext",
			"dom"
		],
		"types": [
			"@dcloudio/types",
			"@uni-helper/uni-ui-types"
		]
	},
	"exclude": [
		"node_modules",
		"unpackage",
		"src/**/*.nvue"
	]
}