
import type { ProfileDetail } from '@/types/member.d.ts';
/** 获取好友列表用户信息 */
export type FriendItem = {
	/*好友在表里的主键id 非唯一 */
	friendId ?: string;

	/*好友Uid */
	friendUid ?: string;

	/*好友关系(1好友,10删除) */
	status ?: number;

	/*好友信息 */
	detail ?: ProfileDetail;

	/*是否点击 */
	isClick ?: boolean;
}
// /** 好友列表返回值 */
// export type FriendsResult = {
// 	/*好友列表 */
// 	list : FriendItem[];

// 	/*好友总量 */
// 	total : number;
// }