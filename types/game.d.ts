/** 游戏赛制对象 */
export type GameFormatDetail = {
	/*游戏赛制id */
	typeId ?: string;

	/*游戏ID */
	gameId ?: string;

	/*游戏赛制名称 */
	typeName ?: string;

	/*游戏选择游戏时候的距离左边位置信息 */
	left ?: number;
	/*游戏选择游戏时候宽度信息 */
	width ?: number;
}
/** 游戏对象 */
export type GameDetail = {
	/*游戏ID */
	gameId ?: string;

	/*游戏名称 */
	gameName ?: string;

	/*游戏图标 */
	img ?: string;

	/*游戏缩略图 */
	icon ?: string;

	/*游戏对应的赛制数组 */
	types ?: GameFormatDetail[];
}

// /** 游戏列表返回值 */
// export type GameListResult = {
// 	/*数据 */
// 	list ?: GameDetail[];
// 	/*总量 */
// 	total ?: number;
// }