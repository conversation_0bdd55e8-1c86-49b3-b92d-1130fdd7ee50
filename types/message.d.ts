import type { ProfileDetail } from '@/types/member.d.ts';

/** 站内信对象 */
export type SiteMsg = {
	/*站内信id */
	messageId : number;
	/*发送者uid */
	fromUid : string;
	/*站内信内容 */
	message : string;
	/*发送人个人信息 */
	fromPlayer : ProfileDetail;
	/*是否点击 */
	isClick : boolean;
}
/** 申请好友用户信息 */
export type FriendApplyItem = {
	/*站内信id */
	messageId : string;
	/*发送者uid */
	fromUid : string;
	/*站内信内容 */
	message : string;
	/*发送人个人信息 */
	fromPlayer : ProfileDetail;
}
/** 消息列表返回值 */
export type MessageListResult = {
	/*站内信 */
	siteMessage : SiteMsg[];

	/*申请好友数组 */
	friendApply : FriendApplyItem[];
	/*消息站内信数量 */
	total : number;
}