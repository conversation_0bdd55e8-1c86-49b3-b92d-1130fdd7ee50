import type { ProfileDetail } from '@/types/member.d.ts';

/** 通用的房间信息 */
export type RoomDetail = {
	/*房间号 */
	rid ?: string;

	/*IM群号,和房间一致 */
	imGroupId ?: string;

	/*会议室号 */
	tvGroupId ?: string;

	/*房间状态(1匹配中,2房间已满,3游戏中,10已解散) */
	status ?: number;

	/*房间类型：1私密 2公开 */
	type ?: number;

	/*观战：1开启观战，2关闭观战 */
	watchType ?: number;

	/*玩家uid */
	playerUid ?: string;

	/*房间所属玩家uid */
	ownerUid ?: string;

	/*游戏类型ID */
	gameTypeId ?: string;

	/*游戏类型名称 */
	gameTypeName ?: string;

	/*游戏code */
	gameId ?: string;

	/*游戏名称 */
	gameName ?: string;

	/*游戏图片 */
	img ?: string;

	/*房主的直播状态（0未直播 1已直播） */
	tvOwnerStatus ?: number;

	/*玩家的直播状态（0未直播 1已直播） */
	tvPlayerStatus ?: number;
	/* 房间人数 */
	// peopleNumber ?: number;

	/*房间成员列表 */
	userList ?: RoomUserItem[];
}

/** 房间列表ite信息 */
export type RoomListItem = {
	/*房间号(唯一) */
	rid ?: string;

	/*这条消息的id */
	messageId ?: number;

	/*房主UID */
	ownerUid ?: string;

	/*房主昵称 */
	ownerNickName ?: string;

	/*房主头像 */
	ownerAvatar ?: string;

	/*大厅房间类型(1好友,2大厅) */
	hallType ?: number;

	/*房间状态(1匹配中,2房间已满,3游戏中,10已解散) */
	status ?: number;

	/*游戏ID */
	gameId ?: string;

	/*游戏名称 */
	gameName ?: string;

	/*游戏赛制ID */
	gameTypeId ?: string;

	/*赛制名称 */
	gameTypeName ?: string;
}

/** 房间成员信息 */
export type RoomUserItem = {
	/*房间号	 */
	rid ?: string;

	/*成员uid	 */
	uid ?: string;

	/*成员类型 (0房主 1玩家 2观众 3裁判 9管理员 ) */
	userType ?: number;

	/*准备状态(0已准备 1未准备) */
	ready ?: number;

	/*玩家信息 */
	detail ?: ProfileDetail;
}

/** 通用的会议 */
export type MeetingDetail = {
	/*房间号 */
	rid : string;

	/*sdkAppID */
	sdkAppID : number;

	/*会议室号,和房间一致 */
	strRoomID : string;

	/*是否开启摄像头 */
	enableCamera : boolean;

	/*是否开启话筒 */
	enableMic : boolean;
}

/** 房间内用户多端在线详情 */
export type RoomOnlineVo = {
	/*是否通过校验 */
	pass : boolean;

	/*校验消息体 */
	message : string;
}

/** 房间列表ite信息 */
// export type RoomListItem = {
// 	/*玩家信息 */
// 	playerLoginRespVO ?: ProfileDetail;

// 	/*房间信息 */
// 	roomDO ?: RoomDetail;
// 	/*1是好友, 2是大厅 */
// 	type ?: number;
// }
/** 邀请房间返回值 */
// export type InviteRoomListResult = {
// 	/*玩家邀请列表 */
// 	inviteList ?: RoomListItem[];

// 	/*大厅房间列表 */
// 	hallRoomList ?: RoomListItem[];
// }