<template>
	<view class="container">

		<view class="sheetItem">
			<view class="wxFriend">
				<uv-icon name="weixin-circle-fill" size="80rpx" color="black"></uv-icon>
			</view>
			<text class="sheetText">微信好友</text>
			<button open-type="share" class="shareBtn" @click="onWXFriendClick"></button>
		</view>
		<!-- <view class="sheetItem" @click="onCopyLinkClick">
			<view class="copyLink">
				<uv-icon name="attach" size="80rpx" color="black"></uv-icon>
			</view>
			<text class="sheetText">复制链接</text>
		</view> -->
	</view>
</template>

<script setup lang="ts">
	const emit = defineEmits(["wxFriendClick", "copyLinkClick"]);
	const onWXFriendClick = () => {
		emit("wxFriendClick");
	};
	const onCopyLinkClick = () => {
		emit("copyLinkClick");
	};
</script>

<style scoped lang="scss">
	.container {
		display: grid;
		grid-template-columns: 25% 25% 25% 25%;
		grid-template-rows: auto;

		.sheetItem {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			position: relative;

			.shareBtn {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, 0);
			}

			.sheetText {
				font-size: 28rpx;
				padding-top: 10rpx;
			}
		}
	}
</style>