<template>
	<view class="container">
		<view class="user-info-container">
			<view class="user-info left-user-info">
				<view class="user-info-bg left-user-info-bg">
					<view class="user-name">{{ onwerUserInfo.detail?.nickName }}</view>
					<view class="image" @click="handleAvatarClick(onwerUserInfo)">
						<uv-image errorIcon="/static/images/icon_defult_avatar.png"
							:src="onwerUserInfo.detail?.avatar || 'defult'" mode="aspectFill" width="160rpx"
							height="118rpx"></uv-image>

					</view>
					<view class="intro-container">
						<view class="lv-container">
							<view class="lv-container-left">
								<view class="lv-name">LV</view>
								<view class="lv-number">{{ onwerUserInfo.detail?.level }}</view>
							</view>
							<!-- 判断是否在线 -->
							<template v-if="onwerUserInfo.detail?.online == '0'">
								<view class="lv-container-right">
									<view class="lv-container-right-self-circle"></view>
								</view>
							</template>
							<template v-else>
								<view class="lv-container-right lv-container-right-opponent">
									<view class="lv-container-right-opponent-circle"></view>
								</view>
							</template>
						</view>
						<view class="info-bg">
							<view class="info-text-bg-container">
								<view v-for="index in 4" :key="index" class="info-text-bg">
									<view class="single-text-bg"></view>
								</view>
							</view>
							<text>{{ onwerUserInfo.detail?.personalSignature }}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="user-info right-user-info">
				<view class="user-info-bg right-user-info-bg">
					<view class="user-name">{{ otherUserInfo.detail?.nickName ?? "匹配中..." }}</view>
					<view class="image" @click="handleAvatarClick(otherUserInfo)">
						<uv-image errorIcon="/static/images/icon_defult_avatar.png"
							:src="otherUserInfo.detail?.avatar || 'defult'" mode="aspectFill" width="160rpx"
							height="118rpx"></uv-image>
					</view>
					<view class="intro-container">
						<view class="lv-container">
							<!-- 对手等级 -->
							<view class="lv-container-left lv-container-left-opponent">
								<view class="lv-name">LV</view>
								<view class="lv-number">{{ otherUserInfo.detail?.level }}</view>
							</view>
							<!-- 判断是否在线 -->
							<template v-if="otherUserInfo.detail?.online == '0'">
								<view class="lv-container-right">
									<view class="lv-container-right-self-circle"></view>
								</view>
							</template>
							<template v-else>
								<view class="lv-container-right lv-container-right-opponent">
									<view class="lv-container-right-opponent-circle"></view>
								</view>
							</template>
						</view>
						<view class="info-bg">
							<view class="info-text-bg-container">
								<view v-for="index in 4" :key="index" class="info-text-bg">
									<view class="single-text-bg"></view>
								</view>
							</view>
							<text>{{ otherUserInfo.detail?.personalSignature }}</text>
						</view>
					</view>

				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import {
		computed, ref,
	} from 'vue';
	import type { RoomDetail, RoomUserItem } from '@/types/room.d.ts';
	import { empty } from '@/uni_modules/uv-ui-tools/libs/function/test.js';
	import { useMemberStore } from '@/stores/index';

	const emit = defineEmits(["onAvatarClick"]);
	const props = defineProps<{ roomDetail : RoomDetail, userArray : RoomUserItem[] }>()

	//房主用户信息
	const onwerUserInfo = computed<RoomUserItem>(() => {
		let user : RoomUserItem = {}

		props.userArray.forEach((item) => {
			if (item.userType == 0) {
				user = item;
			}
		})
		return user
	})
	// 其他用户信息
	const otherUserInfo = computed<RoomUserItem>(() => {
		let user : RoomUserItem = {}

		props.userArray.forEach((item) => {
			if (item.userType == 1) {
				user = item;
			}
		})
		return user
	})

	const handleAvatarClick = (userItem : RoomUserItem) => {
		if (empty(userItem.uid)) {
			return;
		}
		if (userItem.uid == useMemberStore().profile.uid) {
			return
		}
		emit("onAvatarClick", userItem);
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		$width: 260rpx;

		.user-info-container {
			$border-radius: 30rpx;
			display: flex;
			justify-content: space-around;

			.user-info {
				width: $width;
				border-radius: $border-radius;
				padding: 5rpx;
				display: flex;
				overflow: hidden;

				.user-info-bg {
					$spacing: 20rpx;
					display: flex;
					flex-direction: column;
					flex: 1;
					width: 0;
					border-radius: $border-radius;
					// 设置边框
					border: 6rpx solid white;

					.user-name {
						background-color: white;
						margin: $spacing $spacing 0 $spacing;
						border-radius: 20rpx;
						padding: 5rpx $spacing;
						font-size: 18rpx;
						// font-weight: bold;
						font-family: AlimamaShuHeiTi;
						// 省略号
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}

					.image {
						align-self: center;
					}

					.intro-container {
						z-index: 1;
						margin: 0 $spacing $spacing $spacing;
						border-radius: 30rpx;
						// 背景色为灰色透明
						// background-color: rgba(187, 118, 121, 0.7);
						background-color: rgba(166, 166, 166, 0.65);
						display: flex;
						flex-direction: column;

						.lv-container {
							border-radius: 30rpx;
							background-color: white;
							display: flex;
							justify-content: space-between;
							padding: 5rpx 10rpx;

							.lv-container-left {
								display: flex;
								background-color: $color-F7E439;
								border-radius: 8rpx 11rpx 14rpx 11rpx;

								.lv-name {
									font-weight: bold;
									background-color: $color-383838;
									color: white;
									padding: 0rpx 10rpx;
									font-size: 16rpx;
									border-radius: 8rpx 11rpx 14rpx 11rpx;
								}

								.lv-number {
									margin-left: 4rpx;
									margin-right: 10rpx;
									font-weight: bold;
									font-size: 16rpx;
								}
							}

							// 单独设置对手的
							.lv-container-left-opponent {
								background-color: $color-FFC300;

							}

							.lv-container-right {
								background-color: $color-90D102;
								width: 30rpx;
								border-radius: 10rpx;
								display: flex;
								align-items: center;
								justify-content: center;

								.lv-container-right-self-circle {
									width: 10rpx;
									height: 10rpx;
									background-color: white;
									border-radius: 5rpx;
								}

								.lv-container-right-opponent-circle {
									width: 10rpx;
									height: 2rpx;
									background-color: white;
									border-radius: 2rpx;
								}
							}

							.lv-container-right-opponent {
								background-color: $color-E33C64;
							}
						}

						.info-bg {
							$info-text-bg-height: 25rpx;
							$info-text-font-size: 16rpx;
							align-self: center;
							width: 160rpx;
							margin: 8rpx 0 15rpx 0;
							height: calc($info-text-bg-height * 4);
							position: relative;
							display: flex;

							text {
								flex: 1;
								width: 0;
								padding: 0 10rpx;
								position: relative;
								z-index: 2;
								line-height: $info-text-bg-height;
								font-size: $info-text-font-size;
								font-family: DeYiHei;
								display: -webkit-box;
								/* 使元素成为弹性伸缩盒子 */
								-webkit-box-orient: vertical;
								/* 设置盒子里的内容的排列方式为垂直 */
								-webkit-line-clamp: 4;
								/* 限制在一个块元素显示的文本的行数 */
								overflow: hidden;
								/* 隐藏溢出的内容 */
								text-overflow: ellipsis;
								/* 允许在任何字符之间换行 */
								word-break: break-all;
								/* 确保文本能够正常换行 */
								white-space: normal;
							}


							/* 在文本溢出时显示省略号 */
							.info-text-bg-container {
								position: absolute;
								z-index: 1;
								top: 0;
								right: 0;
								bottom: 0;
								left: 0;
							}

							.info-text-bg {
								height: $info-text-bg-height;
								display: flex;
								justify-content: center;
								align-items: center;

								.single-text-bg {
									border-radius: 5rpx;
									width: 0;
									flex: 1;
									background-color: rgba(204, 204, 204, 0.51);
									height: calc($info-text-font-size + 4rpx);
								}
							}
						}
					}
				}

				.left-user-info-bg {
					// 设置背景图片 平铺
					background-image: url("@/static/images/icon_self_info_bg.png");
					background-repeat: repeat;
				}

				.right-user-info-bg {
					// 设置背景图片 平铺
					background-image: url("@/static/images/icon_opponent_info_bg.png");
					background-repeat: repeat;
				}
			}

			.left-user-info {
				// 设置背景图片 平铺
				background-image: url("@/static/images/icon_self_info_bg.png");
				background-repeat: repeat;
			}

			.right-user-info {
				// 设置背景图片 平铺
				background-image: url("@/static/images/icon_opponent_info_bg.png");
				background-repeat: repeat;
			}
		}

	}
</style>