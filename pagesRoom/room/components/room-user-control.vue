<template>
	<view class="container">
		<view class="user-control-container">
			<view class="user-control-top">
				<view class="user-control-top-grid-container">
					<template v-if="userControlType === 1">
						<view class="user-control-top-grid" @click="onFriendBtnClick">
							<uv-icon name="friends" custom-prefix="custom-icon" size="40rpx" color="black"></uv-icon>
						</view>
						<view class="give-like">邀请好友</view>
					</template>
					<template v-if="userControlType === 2 || userControlType === 3">
						<view class="user-control-top-grid" @click="onGiveLikeBtnClick">
							<uv-icon name="thumb-up" size="40rpx"
								:color="props.isGiveLike ? '#1AFA28' : 'black'"></uv-icon>
						</view>
						<view class="give-like">友善的对手</view>
					</template>

				</view>

				<view class="user-control-top-grid-container">


					<template v-if="userControlType === 1">
						<view class="user-control-top-grid" @click="onShareInvitationClick">
							<uv-icon name="share" size="40rpx" color="black"></uv-icon>
						</view>
						<view class="give-like">分享邀请</view>
					</template>
					<template v-if="userControlType === 2 || userControlType === 3">
						<view class="user-control-top-grid" @click="onAddFriendClick">
							<template v-if="props.isAddFriend">
								<uv-icon name="checkmark-circle" size="40rpx" color="#1AFA28"></uv-icon>
							</template>

							<template v-else>
								<uv-icon name="tianjiahaoyou" custom-prefix="custom-icon" size="40rpx"
									color="black"></uv-icon>
							</template>

						</view>
						<view class="add-friend">添加好友</view>
					</template>

				</view>

				<view class="user-control-top-grid-container">

					<template v-if="userControlType === 1 || userControlType === 3">
						<view class="user-control-top-grid-red" @click="handleBackToHomeClick">
							<uv-icon name="close" size="40rpx" color="white"></uv-icon>
						</view>
						<template v-if="userControlType === 1">
							<view class="give-like">返回首页</view>
						</template>
						<template v-else>
							<view class="give-like">退出房间</view>
						</template>
					</template>
					<template v-if="userControlType === 2">
						<view class="user-control-top-grid" @click="onPleaseLeaveRoomClick">
							<uv-icon name="share-square" size="40rpx" color="black"></uv-icon>
						</view>
						<view class="get-out">请离对手</view>
					</template>

				</view>

			</view>
			<view class="user-control-bottom">房间号：{{props.roomDetail.rid}}</view>
		</view>
	</view>
	<!-- 好友列表弹窗 -->
	<view>
		<uv-popup ref="friendListPopup" :safeAreaInsetBottom="false" mode="bottom" round="80rpx"
			:closeOnClickOverlay="false">
			<tcg-custom-popup v-if="isFriendListShow" @closeBtnClick="friendListPopupCloseBtnClick">
				<tcg-friend-list style="height: 100%" @onInviteFriend="onInviteFriend"></tcg-friend-list>
			</tcg-custom-popup>
		</uv-popup>
	</view>
</template>

<script setup lang="ts">
	import type { RoomDetail, RoomUserItem } from '@/types/room.d.ts';
	import { useMemberStore } from '@/stores/index'
	import { computed, ref } from 'vue';
	import { FriendItem } from '@/types/friend.d.ts';
	import { inviteJoinRoomAPI } from '@/services/room';


	const emit = defineEmits(["backToHome", "giveLikeBtnClick", "addFriendClick", "shareInvitationClick", "pleaseLeaveRoomClick"]);

	const props = defineProps<{ roomDetail : RoomDetail, userArray : RoomUserItem[], isGiveLike : boolean, isAddFriend : boolean }>()
	const userControlType = computed(() => {
		// 1.房主，且只有自己
		if (useMemberStore().profile.uid === props.roomDetail?.ownerUid && props.userArray.length === 1) {
			return 1;
		}
		// 2.房主，且有其他人
		if (useMemberStore().profile.uid === props.roomDetail?.ownerUid && props.userArray.length > 1) {
			return 2
		}
		// 3.非房主
		if (useMemberStore().profile.uid !== props.roomDetail?.ownerUid) {
			return 3
		}
		return 0
	})

	const friendListPopup = ref();
	const isFriendListShow = ref(false);
	const onFriendBtnClick = () => {
		friendListPopup.value.open();
		isFriendListShow.value = true;
	}
	const friendListPopupCloseBtnClick = () => {
		friendListPopup.value.close();
		// 300ms后再设置为false，否则会导致弹窗关闭时，弹窗内容会闪一下
		setTimeout(() => {
			isFriendListShow.value = false;
		}, 300);
	}
	const onInviteFriend = async (item : FriendItem) => {
		uni.showLoading({
			title: ''
		})
		inviteJoinRoomAPI(props.roomDetail.rid!, item.detail.uid!).then(() => {
			uni.hideLoading();
			friendListPopupCloseBtnClick();
			uni.showToast({
				title: "已发送邀请",
				icon: 'none'
			})
		})
	}
	const handleBackToHomeClick = () => {
		emit("backToHome")
	}
	const onGiveLikeBtnClick = () => {
		emit("giveLikeBtnClick")
	}
	const onAddFriendClick = () => {
		emit("addFriendClick")
	}
	const onShareInvitationClick = () => {
		emit("shareInvitationClick")
	}
	const onPleaseLeaveRoomClick = () => {
		emit("pleaseLeaveRoomClick")
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		$width: 260rpx;

		.user-control-container {
			// 计算margin
			$margin: calc((750rpx - $width * 2) / 4);
			margin: 0 $margin;
			display: flex;
			flex-direction: column;
			border-radius: 40rpx 20rpx;
			background-color: white;

			.user-control-top {
				border-radius: 40rpx 20rpx;
				display: flex;
				justify-content: space-evenly;
				background-color: $color-383838;
				font-family: AlimamaShuHeiTi;

				.user-control-top-grid-container {
					display: flex;
					flex-direction: column;
					margin: 20rpx 0;
					justify-content: center;
					align-items: center;

					.user-control-top-grid {
						height: 60rpx;
						width: 60rpx;
						background-color: white;
						display: flex;
						justify-content: center;
						border-radius: 15rpx;
					}

					.user-control-top-grid-red {
						height: 60rpx;
						width: 60rpx;
						background-color: red;
						display: flex;
						justify-content: center;
						border-radius: 15rpx;
					}

					.give-like {
						margin-top: 10rpx;
						font-size: 14rpx;
						color: white;
					}

					.add-friend {
						margin-top: 10rpx;
						font-size: 14rpx;
						color: white;
					}

					.get-out {
						margin-top: 10rpx;
						font-size: 14rpx;
						color: white;
					}
				}
			}

			.user-control-bottom {
				margin: 12rpx 0;
				text-align: center;
				color: $color-383838;
				font-size: 16rpx;
				font-family: YouSheBiaoTiHei;
			}
		}
	}
</style>