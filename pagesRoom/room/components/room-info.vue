<template>
	<view class="container">
		<view class="game-logo" @click="handelChangeGame">
			<image class="logo" :src="props.roomDetail.img" mode="widthFix"></image>
		</view>
		<view class="game-mode-box" @click="handelChangeGame">
			<view class="game-mode">{{props.roomDetail.gameTypeName}}</view>
		</view>
		<view class="room-info">
			<view class="isWatch">
				<uv-button :icon="roomWatchIcon" :customTextStyle="roomInfoTextStyle" :custom-style="isWatchBtnStyle"
					color="#fff" :text="roomWatchString" @click="handleRoomWatchClick"></uv-button>
			</view>
			<view class="isPublic">
				<uv-button :icon="roomTypeIcon" :customTextStyle="roomInfoTextStyle" :custom-style="isPublicBtnStyle"
					color="#fff" :text="roomTypeString" @click="handleRoomTypeClick"></uv-button>
			</view>
		</view>
	</view>

</template>

<script setup lang="ts">
	import type { RoomDetail } from '@/types/room.d.ts';
	import { computed, reactive, ref } from 'vue';


	const props = defineProps<{ roomDetail : RoomDetail }>()
	const isWatchBtnStyle = {
		height: '65rpx',
		paddingRight: '40rpx',
		color: 'black',
		borderRadius: '20rpx 10rpx',
	};
	const isPublicBtnStyle = {
		height: '65rpx',
		paddingRight: '40rpx',
		color: 'black',
		borderRadius: '10rpx 20rpx',
	};
	const roomInfoTextStyle = {
		fontSize: '25rpx',
	};
	const emit = defineEmits(["roomTypeClick", "roomWatchClick", "roomChangeGame"]);
	const roomTypeString = computed(() => props.roomDetail?.type === 1 ? "私密房间" : "公开房间")
	const roomTypeIcon = computed(() => props.roomDetail?.type === 1 ? "lock" : "lock-open")
	const roomWatchString = computed(() => props.roomDetail?.watchType === 1 ? "允许观战" : "禁止观战")
	const roomWatchIcon = computed(() => props.roomDetail?.watchType === 1 ? "eye" : "eye-off-outline")
	const handleRoomTypeClick = () => {
		emit("roomTypeClick")
	}
	const handleRoomWatchClick = () => {
		emit("roomWatchClick")
	}

	const handelChangeGame = () => {
		emit("roomChangeGame")
	}
</script>

<style scoped lang="scss">
	.container {
		background-color: $color-FFF3BA;
		display: flex;
		padding-left: 90rpx;
		justify-content: space-between;
		align-items: center;

		// 盒子只有底部有5px的阴影
		box-shadow: 0 8rpx 8rpx -8rpx rgba(0, 0, 0, 0.5);

		.game-logo {
			display: flex;
			align-items: center;

			.logo {
				// margin: 00rpx 0;
				width: 180rpx;
			}
		}

		.game-mode-box {
			background-color: $color-383838;
			padding: 2rpx 6rpx 6rpx 2rpx;
			border-radius: 10rpx 18rpx 25rpx 18rpx;
			overflow: hidden;

			.game-mode {
				padding: 8rpx 40rpx;
				background-color: white;
				font-size: 25rpx;
				border-radius: 10rpx 18rpx 25rpx 18rpx;
				font-family: DeYiHei;
			}
		}

		.room-info {
			align-self: stretch;
			background-color: $color-383838;
			border-radius: 25rpx 0 0 25rpx;
			display: flex;
			flex-direction: column;
			padding: 8rpx 20rpx 10rpx 8rpx;
			font-family: AlimamaShuHeiTi;

			.isWatch {
				flex: 1;
				margin-bottom: 6rpx;
			}

			.isPublic {
				flex: 1;
			}
		}
	}
</style>