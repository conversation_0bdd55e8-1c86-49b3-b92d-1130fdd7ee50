<template>
	<view class="container">
		<!-- 		<uv-navbar bg-color="rgba(0,0,0,0)" @leftClick="onLeftClick"></uv-navbar> -->
		<tcg-header @onRoomInviteFriend="onRoomInviteFriend"></tcg-header>

		<view style="flex: 1;"></view>

		<room-info :roomDetail="roomDetail" @roomChangeGame="roomChangeGame" @roomTypeClick="roomTypeClick"
			@roomWatchClick="roomWatchClick"></room-info>

		<view style="flex: 1;"></view>

		<room-user-info :roomDetail="roomDetail" :userArray="userArray"
			@onAvatarClick="onReportFriendClick"></room-user-info>

		<view style="flex: 1;"></view>

		<room-user-control :roomDetail="roomDetail" :userArray="userArray" :isGiveLike="isGiveLike"
			:isAddFriend="isAddFriend" @backToHome="backToHome" @giveLikeBtnClick="giveLikeBtnClick"
			@addFriendClick="addFriendClick" @shareInvitationClick="shareInvitationClick"
			@pleaseLeaveRoomClick="pleaseLeaveRoomClick"></room-user-control>

		<view style="flex: 1;"></view>

		<view class="fight" @click="startFight">
			<view class="room-owner-ready" :class="{'visibility-hidden': !ownerIsReady}">
				<image
					src="https://img.js.design/assets/img/65633610d012312568672c4d.png#2af843932d0b958f0b7f05b752f7de58"
					class="mark"></image>
			</view>
			<view v-show="playerIsReady" class="room-player-ready">
				<image
					src="https://img.js.design/assets/img/65633610d012312568672c4d.png#2af843932d0b958f0b7f05b752f7de58"
					class="mark"></image>
			</view>
			<image
				:src="isCanStartFight ? '/pagesRoom/static/images/duel_start.png' : '/pagesRoom/static/images/duel_not_start.png'"
				mode="" class="start-fight-image"></image>
		</view>

		<view style="flex: 1;"></view>

		<!-- <room-fight-invite></room-fight-invite> -->
		<!-- <view style="flex: 1;"></view> -->

	</view>


	<view>
		<uv-action-sheet ref="shareActionSheet" description="分享到" :round="10">
			<view>
				<share-action-sheet-content @copyLinkClick="copyLinkClick"
					@wxFriendClick="wxFriendClick"></share-action-sheet-content>
				<uv-gap height="40rpx"></uv-gap>
			</view>
		</uv-action-sheet>
	</view>

	<view>
		<uv-popup :overlay-style="{background: 'rgba(56, 56, 56, 0.98)'}" ref="reportMaskingPopup" bgColor="none"
			:safeAreaInsetBottom="false" mode="center" :closeOnClickOverlay="false">
			<tcg-report-masking v-if="isReportMaskingShow" @onCloseReportPopup="onCloseReportPopup"
				@onCommitReportClick="onCommitReportClick" @onMaskingClick="onMaskingClick"></tcg-report-masking>
		</uv-popup>
	</view>

	<view>
		<uv-popup :overlay-style="{background: 'rgba(56, 56, 56, 0.8)'}" ref="tcgModalPopup" bgColor="none"
			:safeAreaInsetBottom="false" mode="center" :closeOnClickOverlay="false">
			<tcg-modal :message="modalMessage" :messageType="modalMessageType" :type="modalType"
				@cancel="onCancelTcgMoadlPopup" @confirm="onConfirmTcgMoadlPopup"></tcg-modal>
		</uv-popup>
	</view>

	<view>
		<uv-popup :overlay-style="{background: 'rgba(56, 56, 56, 0.8)'}" ref="tcgChooseGamePopop" bgColor="none"
			:safeAreaInsetBottom="false" mode="center" duration="0" @maskClick="handleChooseGameCancel">

			<tcg-choose-game-modal v-if="isShowChooseGamePopop" :pageGameInfo="pageGameInfo"
				@cancel="handleChooseGameCancel" @confirm="handleChooseGameConfirm"></tcg-choose-game-modal>
		</uv-popup>
	</view>
</template>

<script setup lang="ts">
	import RoomInfo from '@/pagesRoom/room/components/room-info.vue'
	import RoomUserInfo from '@/pagesRoom/room/components/room-user-info.vue'
	import RoomUserControl from '@/pagesRoom/room/components/room-user-control.vue'
	// import RoomFightInvite from '@/pages/index/components/room-fight-invite.vue'
	import ShareActionSheetContent from '@/pagesRoom/room/components/share-action-sheet-content'
	import { ReportParams } from '@/types/Report.d.ts';
	import {
		onShow,
		onHide,
		onLoad,
		onUnload,
		onReady,
		onShareAppMessage,
	} from '@dcloudio/uni-app';
	import {
		ref,
		reactive,
		getCurrentInstance,
		provide,
		ComputedRef,
		computed,
	} from 'vue';
	import { useMemberStore } from '@/stores/index'
	import type { RoomDetail, RoomUserItem } from '@/types/room.d.ts';
	import { inviteJoinRoomAPI, updateRoomAPI, leaveRoomAPI, createRoomAPI, giveLikeAPI, getRoomDetailAPI, pleaseLeaveRoomAPI, getRoomUserListAPI, getRoomUserDetailAPI, createTVRoomAPI, enterTVRoomAPI, getUserOnlineRoomAPI, roomPlayerReadyAPI } from '@/services/room'
	import { options as txOption } from '@/utils/TcgIMHelper'
	import TencentCloudChat, { Message } from '@tencentcloud/chat';
	import { FriendItem } from '@/types/friend.d.ts';
	import { useRoomStore } from '@/stores/modules/room'
	import { addFriendAPI, maskingFriendAPI } from '@/services/friend';
	import { initiateReportAPI } from '@/services/report'
	import { FriendApplyItem } from '@/types/message.d.ts';
	import { UserLikeVO } from '@/types/member.d.ts';
	import { page } from '@/uni_modules/uv-ui-tools/libs/function/index.js';
	import { getRoomMultipleOnlineAPI } from '@/services/room';
	import { getMemberMultipleOnlineAPI } from '@/services/profile';
	const { ctx } : any = getCurrentInstance();

	const roomStore = useRoomStore()
	let userArray = ref<RoomUserItem[]>([])
	let roomDetail = ref<RoomDetail>()

	onLoad((options) => {
		const roomInfo = JSON.parse(decodeURIComponent(options!.roomDetail))
		roomDetail.value = roomInfo;
		getGroupUserListInfo(roomInfo.rid);
		roomStore.setRoomDetail(roomDetail.value)
		getUserOnlineRoom()
		createEvent()
	});
	onUnload(() => {
		removeEvent()
	});

	onReady(() => {
		// 设置分享参数
		onShareAppMessage(() => {
			return {
				title: `房已开好！${roomDetail.value.gameName}，${roomDetail.value.gameTypeName}模式，速来！`,
				path: `/pages/loading/loading?roomId=${roomDetail.value.rid}&roomOwnerUid=${roomDetail.value.ownerUid}`,
				imageUrl: "https://www.duelchannel.com/min-api/file/resource/shareImage.jpg",
			}
		})
	});


	const onTcgWebOwnerStartFighting = (roomInfo : RoomDetail) => {

		// 如果已经在房间内了,则不执行这个方法
		if (page().includes("pagesMeeting/pages/meeting/meeting")) {
			return
		}

		if (roomInfo.rid != roomDetail.value?.rid) {
			return
		}
		startFight()
	}

	// 创建事件监听
	const createEvent = () => {
		uni.$on('TcgAppOnShow', tcgAppOnShow)
		uni.$on('updateRoomInfo', updateRoomInfo)
		uni.$on('onOtherUserEnterRoom', onOtherUserEnterRoom)
		uni.$on('onOtherUserLeaveRoom', onOtherUserLeaveRoom)
		uni.$on('updateRoomUserOnLineStatus', updateRoomUserOnLineStatus)
		uni.$on('updateRoomPlayerIsGameReady', updateRoomPlayerIsGameReady)
		uni.$on('onTcgWebOwnerStartFighting', onTcgWebOwnerStartFighting);
		uni.$on('enterTVRoom', enterTVRoom)
	}
	// 移除事件监听
	const removeEvent = () => {
		uni.$off('TcgAppOnShow', tcgAppOnShow)
		uni.$off('updateRoomInfo', updateRoomInfo);
		uni.$off('onOtherUserEnterRoom', onOtherUserEnterRoom)
		uni.$off('onOtherUserLeaveRoom', onOtherUserLeaveRoom)
		uni.$off('updateRoomUserOnLineStatus', updateRoomUserOnLineStatus)
		uni.$off('updateRoomPlayerIsGameReady', updateRoomPlayerIsGameReady)
		uni.$off('onTcgWebOwnerStartFighting', onTcgWebOwnerStartFighting);
		uni.$off('enterTVRoom', enterTVRoom)
	}

	const tcgAppOnShow = () => {
		updateRoomInfo(roomDetail.value.rid)
		getGroupUserListInfo(roomDetail.value?.rid)
	}


	const updateRoomInfo = (roomId : string) => {
		if (roomId != roomDetail.value?.rid) {
			return
		}

		getRoomDetailAPI(roomId).then((res) => {
			roomDetail.value!.gameId = res.data.gameId;
			roomDetail.value!.gameName = res.data.gameName;
			roomDetail.value!.img = res.data.img;
			roomDetail.value!.gameTypeId = res.data.gameTypeId;
			roomDetail.value!.gameTypeName = res.data.gameTypeName;
			roomDetail.value!.type = res.data.type;
			roomDetail.value!.watchType = res.data.watchType;
		})
	}
	let messageTime : number = 0;
	const updateRoomUserOnLineStatus = (message : Message) => {
		const userDefinedField = JSON.parse(message.payload.userDefinedField);
		const data = userDefinedField.data
		const time = message.time;
		if (data.imGroupId != roomDetail.value?.rid) {
			return;
		}
		if (time <= messageTime) {
			console.log("上线,暂离的顺序错啦");
			return
		}
		messageTime = time
		let user = userArray.value.find(user => user.uid == data.uid);
		if (user) {
			user.detail.online = data.online
		}
	}

	const onOtherUserEnterRoom = (data : any) => {
		if (data.groupProfile.groupID == roomDetail.value?.rid) {
			getGroupUserListInfo(roomDetail.value?.rid)
		}
	}
	const onOtherUserLeaveRoom = (data : any) => {
		if (data.groupProfile.groupID === roomDetail.value?.rid) {
			getGroupUserListInfo(roomDetail.value?.rid)
		}
	}


	// 获取群资料
	const getGroupUserListInfo = (groupId : string) => {
		getRoomUserListAPI(groupId).then((res) => {
			userArray.value.length = 0
			for (let item of res.data) {
				if (item.userType == 0 || item.userType == 1) {
					userArray.value.push(item)
				}
			}
		})
	};
	function backToHome() {
		uni.showLoading({
			title: ''
		})
		leaveRoomAPI(roomDetail.value?.rid!).then((res) => {
			uni.hideLoading();
			if (useMemberStore().profile.uid == roomDetail.value.ownerUid) {
				roomStore.clearRoomDetail();
				uni.navigateBack();
			} else {
				let roomDetail = encodeURIComponent(JSON.stringify(res.data));
				ctx.$uv.route({
					url: '/pagesRoom/room/room',
					type: 'redirectTo',
					params: {
						roomDetail: roomDetail
					}
				})
			}
		});
	}

	const getUserOnlineRoom = async () => {
		if (roomStore.roomDetail.status == 3) {
			let meetingDetail = JSON.stringify({
				rid: roomStore.roomDetail.rid, // 直接在这里填入房间号
				sdkAppID: txOption.SDKAppID, // 直接填入sdkAppID
				strRoomID: roomStore.roomDetail.tvGroupId, // 直接填入会议室号
				enableCamera: true, // 设置是否开启摄像头
				enableMic: true, // 设置是否开启话筒
			});
			ctx.$uv.route({
				url: '/pagesMeeting/pages/meeting/meeting',
				params: {
					meetingDetail: meetingDetail
				}
			})
		}
	}

	const giveLikeBtnClick = () => {
		if (useMemberStore().profile.uid == roomDetail.value?.ownerUid) {
			for (let item of userArray.value) {
				if (item.userType == 1) {
					giveLike(item.uid!)
					break;
				}
			}
		} else {
			giveLike(roomDetail.value?.ownerUid!)
		}
	}

	const isGiveLike = ref(false);
	const giveLike = (uid : string) => {
		uni.showLoading({
			title: ''
		})
		giveLikeAPI({
			receiverUid: uid,
			bizType: 1,
			rid: roomDetail.value.rid
		}).then((res) => {
			uni.hideLoading();
			uni.showToast({
				title: '已给好友点赞',
				icon: 'none'
			})
			isGiveLike.value = true
		}).catch((error) => {
			if (error.message == '不能重复点赞') {
				isGiveLike.value = true
			}
		})
	}

	const isAddFriend = ref(false);

	const addFriendClick = async () => {
		if (useMemberStore().profile.uid == roomDetail.value?.ownerUid) {
			for (let item of userArray.value) {
				if (item.userType == 1) {
					addFriend(item.uid!)
					break;
				}
			}
		} else {
			addFriend(roomDetail.value?.ownerUid!)
		}
	}

	const addFriend = (uid : string) => {
		uni.showLoading({
			title: ''
		})
		addFriendAPI(uid).then(() => {
			uni.hideLoading();
			uni.showToast({
				title: '已发送好友申请',
				icon: 'none'
			})
			isAddFriend.value = true
		}).catch((error) => {
			if (error.message == '已经是好友关系') {
				isAddFriend.value = true
			}
		})
	}

	//房主用户信息
	const onwerUserInfo = computed<RoomUserItem>(() => {
		let user : RoomUserItem = {}

		userArray.value.forEach((item) => {
			if (item.userType == 0) {
				user = item;
			}
		})
		return user
	})
	// 其他用户信息
	const playerUserInfo = computed<RoomUserItem>(() => {
		let user : RoomUserItem = {}

		userArray.value.forEach((item) => {
			if (item.userType == 1) {
				user = item;
			}
		})
		return user
	})

	const isCanStartFight = computed(() => {
		return ownerIsReady.value && playerIsReady.value
	})

	const ownerIsReady = computed(() => {
		return onwerUserInfo.value.detail?.online == '0';
	})

	const playerIsReady = computed(() => {
		if (playerUserInfo.value.ready == 0 && playerUserInfo.value.detail?.online == '0') {
			return true
		}
		return false
	})

	const startFight = () => {
		if (roomDetail.value!.ownerUid != useMemberStore().profile.uid) {
			roomPlayerReady()
			return;
		}
		if (!isCanStartFight.value) {
			uni.showToast({
				title: '请等待对手准备',
				icon: 'none'
			})
			return
		}
		uni.showLoading({
			title: ''
		})
		getRoomMultipleOnlineAPI(roomDetail.value.rid).then((res) => {
			if (res.data.pass) {
				createTVRoom({ isNeedLoading: false })
			} else {
				uni.hideLoading()
				modalMessageType.value = 'pcNotOnLine';
				modalMessage.value = res.data.message
				modalType.value = 2;
				tcgModalPopup.value.open();
			}
		})
	}
	const roomPlayerReady = () => {
		uni.showLoading({
			title: ''
		})
		getMemberMultipleOnlineAPI(useMemberStore().profile.uid).then((res) => {
			if (!res.data.multipleOnline) {
				uni.hideLoading();
				modalMessageType.value = 'pcNotOnLine';
				modalMessage.value = '开启对战需要网页端配合,请打开网页端';
				modalType.value = 2;
				tcgModalPopup.value.open();
				return
			}
			roomPlayerReadyAPI({
				rid: roomDetail.value.rid,
				playerUid: useMemberStore().profile.uid,
				ready: playerUserInfo.value.ready == 0 ? 1 : 0
			}).then(() => {
				uni.hideLoading();
			})
		});


	}

	const updateRoomPlayerIsGameReady = (data : any) => {
		if (data.data.rid != roomDetail.value?.rid) {
			return
		}

		let user = userArray.value.find(user => user.uid == data.data.uid);
		if (user) {
			user.ready = data.data.ready
		}
	}


	const createTVRoom = (data : { isNeedLoading : boolean }) => {
		if (data.isNeedLoading) {
			uni.showLoading({
				title: ''
			})
		}
		createTVRoomAPI(roomDetail.value.rid, roomDetail.value.tvGroupId).then((res) => {
			uni.hideLoading();
		})
	}
	const enterTVRoom = () => {
		// 如果已经在房间内了,则不执行这个方法
		if (page().includes("pagesMeeting/pages/meeting/meeting")) {
			return
		}
		let meetingDetail = JSON.stringify({
			rid: roomDetail.value.rid, // 直接在这里填入房间号
			sdkAppID: txOption.SDKAppID, // 直接填入sdkAppID
			strRoomID: roomDetail.value.tvGroupId, // 直接填入会议室号
			enableCamera: true, // 设置是否开启摄像头
			enableMic: true, // 设置是否开启话筒
		});

		if (roomDetail.value!.ownerUid == useMemberStore().profile.uid) {
			ctx.$uv.route({
				url: '/pagesMeeting/pages/meeting/meeting',
				params: {
					meetingDetail: meetingDetail
				}
			})
		} else {
			enterTVRoomAPI(roomDetail.value.rid).then((res) => {
				ctx.$uv.route({
					url: '/pagesMeeting/pages/meeting/meeting',
					params: {
						meetingDetail: meetingDetail
					}
				})
			})
		}
	}


	const roomTypeClick = async () => {
		const useMember = useMemberStore();
		if (roomDetail.value!.ownerUid !== useMember.profile.uid) {
			uni.showToast({
				title: '只有房主才能操作哦',
				icon: 'none'
			})
			return;
		}
		if (roomDetail.value) {
			updateRoomAPI({
				rid: roomDetail.value.rid!,
				type: roomDetail.value.type === 1 ? 2 : 1,
				gameId: roomDetail.value.gameId!,
				gameTypeId: roomDetail.value.gameTypeId!,
				watchType: roomDetail.value.watchType!
			})
		}
	}
	const roomWatchClick = async () => {
		const useMember = useMemberStore();
		if (roomDetail.value!.ownerUid !== useMember.profile.uid) {
			uni.showToast({
				title: '只有房主才能操作哦',
				icon: 'none'
			})
			return;
		}
		updateRoomAPI({
			rid: roomDetail.value!.rid!,
			watchType: roomDetail.value!.watchType === 1 ? 2 : 1,
			gameId: roomDetail.value!.gameId!,
			gameTypeId: roomDetail.value!.gameTypeId!,
			type: roomDetail.value!.type!
		})
	}

	// 界面上最终显示的游戏信息
	const pageGameInfo : ComputedRef<UserLikeVO> = computed(() => {
		return {
			gameId: roomDetail.value!.gameId!,
			gameName: roomDetail.value!.gameName!,
			img: roomDetail.value!.img!,
			gameTypeName: roomDetail.value!.gameTypeName!,
			gameTypeId: roomDetail.value!.gameTypeId!,
		}
	})

	const tcgChooseGamePopop = ref()
	const isShowChooseGamePopop = ref(false)

	const handleChooseGameCancel = () => {
		tcgChooseGamePopop.value.close();
		setTimeout(() => {
			isShowChooseGamePopop.value = false
		}, 20);
	}
	const handleChooseGameConfirm = (chooseGame : UserLikeVO) => {
		tcgChooseGamePopop.value.close();
		setTimeout(() => {
			isShowChooseGamePopop.value = false
		}, 20);

		updateRoomAPI({
			rid: roomDetail.value!.rid!,
			gameId: chooseGame.gameId,
			gameTypeId: chooseGame.gameTypeId,
			type: roomDetail.value!.type!,
			watchType: roomDetail.value!.watchType!
		})
	}


	const roomChangeGame = () => {
		const useMember = useMemberStore();
		if (roomDetail.value!.ownerUid !== useMember.profile.uid) {
			uni.showToast({
				title: '只有房主才能操作哦',
				icon: 'none'
			})
			return;
		}
		tcgChooseGamePopop.value.open()
		isShowChooseGamePopop.value = true
	}

	const onRoomInviteFriend = async (item : FriendItem) => {
		uni.showLoading({
			title: ''
		})
		inviteJoinRoomAPI(roomDetail.value!.rid!, item.detail.uid!).then(() => {
			uni.hideLoading();
			uni.showToast({
				title: "已发送邀请",
				icon: 'none'
			})
		})
	}

	const shareActionSheet = ref();
	const shareInvitationClick = () => {
		shareActionSheet.value.open();
	}
	const wxFriendClick = () => {
		shareActionSheet.value.close();
	}
	const copyLinkClick = () => {
		uni.showToast({
			title: '复制链接',
			icon: 'none'
		})
		shareActionSheet.value.close();
	}

	const reportUserItem = ref<RoomUserItem>()
	const reportMaskingPopup = ref();
	const isReportMaskingShow = ref(false);

	const onReportFriendClick = (userItem : RoomUserItem) => {
		reportUserItem.value = userItem
		reportMaskingPopup.value.open();
		isReportMaskingShow.value = true;
	}
	const onCloseReportPopup = () => {
		reportMaskingPopup.value.close();
		// 300ms后再设置为false，否则会导致弹窗关闭时，弹窗内容会闪一下
		setTimeout(() => {
			isReportMaskingShow.value = false;
		}, 300);
	}
	const onMaskingClick = () => {
		reportMaskingPopup.value.close();
		// 300ms后再设置为false，否则会导致弹窗关闭时，弹窗内容会闪一下
		setTimeout(() => {
			isReportMaskingShow.value = false;
			maskingRoomUser()
		}, 300);
	}

	const maskingRoomUser = async () => {
		uni.showLoading({
			title: ''
		})
		maskingFriendAPI(reportUserItem.value.uid).then(() => {
			uni.hideLoading();
			uni.showToast({
				title: '已拉黑对手',
				icon: 'none'
			})
		})
	}

	const onCommitReportClick = (reportParams : ReportParams) => {
		reportMaskingPopup.value.close();
		// 300ms后再设置为false，否则会导致弹窗关闭时，弹窗内容会闪一下
		setTimeout(() => {
			isReportMaskingShow.value = false;
			initiateReport(reportParams)
		}, 300);
	}

	const initiateReport = (reportParams : ReportParams) => {

		uni.showLoading({
			title: ''
		})
		initiateReportAPI({
			fromUid: useMemberStore().profile.uid,
			toUid: reportUserItem.value.uid,
			rid: reportUserItem.value.rid,
			type: reportParams.typeId,
			message: reportParams.message
		}).then((res) => {
			uni.hideLoading();
			uni.showToast({
				title: '已举报此人',
				icon: 'none'
			})
		})
	}


	const pleaseLeaveRoomClick = () => {

		if (useMemberStore().profile.uid != roomDetail.value?.ownerUid) {
			return
		}

		for (let item of userArray.value) {
			if (item.userType == 1) {
				modalMessageType.value = 'isPleaseUserLeaveRoom';
				modalMessage.value = '确定要请离对手吗？';
				pleaseLeaveRoomItem = item;
				tcgModalPopup.value.open();
				break;
			}
		}
	}
	let pleaseLeaveRoomItem : RoomUserItem;
	const pleaseLeaveRoom = (uid : string) => {

		uni.showLoading({
			title: ''
		})
		pleaseLeaveRoomAPI(roomDetail.value!.rid!, uid).then((res) => {
			uni.hideLoading();
			uni.showToast({
				title: '已请离对手',
				icon: 'none'
			})
		})
	}

	const tcgModalPopup = ref();
	// isDeleteFriend  : 是否删除好友
	const modalMessageType = ref('');
	// 是否删除好友
	const modalMessage = ref('');
	const modalType = ref(1);

	const onCancelTcgMoadlPopup = (modalMessageType : string) => {
		console.log(modalMessageType);
		tcgModalPopup.value.close();
	}
	const onConfirmTcgMoadlPopup = (modalMessageType : string) => {
		tcgModalPopup.value.close();
		// 延迟300毫秒,等待动画结束
		setTimeout(() => {
			if (modalMessageType == 'isDeleteFriend') {
				if (deleteFriendCallback) {
					deleteFriendCallback(deleteFriendItem); // 调用回调时传递参数
				}
			} else if (modalMessageType == 'isMaskingFriend') {
				if (maskingFriendCallback) {
					maskingFriendCallback(maskingFriendItem); // 调用回调时传递参数
				}
			} else if (modalMessageType == 'isAgreeFriendRequest') {
				if (friendRequestCallback) {
					friendRequestCallback(friendRequestItem, "1"); // 调用回调时传递参数
				}
			} else if (modalMessageType == 'isRefuseFriendRequest') {
				if (friendRequestCallback) {
					friendRequestCallback(friendRequestItem, "2"); // 调用回调时传递参数
				}
			} else if (modalMessageType == 'isPleaseUserLeaveRoom') {
				pleaseLeaveRoom(pleaseLeaveRoomItem.uid!)
			} else if (modalMessageType == 'pcNotOnLine') {
				modalType.value = 1;
			}
		}, 300)
	}


	let deleteFriendCallback : DeleteFriendtCallbackFunction;
	let deleteFriendItem : FriendItem;
	// 定义接受一个参数的回调函数类型
	type DeleteFriendtCallbackFunction = (item : FriendItem) => void;
	const handleDeleteFriend = (item : FriendItem, callback : DeleteFriendtCallbackFunction) => {
		deleteFriendCallback = callback;
		deleteFriendItem = item;
		modalMessageType.value = 'isDeleteFriend';
		modalMessage.value = '是否删除好友';
		tcgModalPopup.value.open();
	}

	provide('handleDeleteFriend', handleDeleteFriend);


	let maskingFriendCallback : DeleteFriendtCallbackFunction;
	let maskingFriendItem : FriendItem;
	// 定义接受一个参数的回调函数类型
	type MaskingFriendtCallbackFunction = (item : FriendItem) => void;
	const handleMaskingFriend = (item : FriendItem, callback : MaskingFriendtCallbackFunction) => {
		maskingFriendCallback = callback;
		maskingFriendItem = item;
		modalMessageType.value = 'isMaskingFriend';
		modalMessage.value = '是否拉黑好友';
		tcgModalPopup.value.open();
	}
	provide('handleMaskingFriend', handleMaskingFriend);


	let friendRequestCallback : FriendRequestCallbackFunction;
	let friendRequestItem : FriendApplyItem;
	// 定义接受一个参数的回调函数类型
	type FriendRequestCallbackFunction = (item : FriendApplyItem, type : string) => void;
	const handleFriendRequest = (item : FriendApplyItem, type : string, callback : FriendRequestCallbackFunction) => {
		friendRequestItem = item;
		friendRequestCallback = callback;
		if (type == '1') {
			modalMessageType.value = 'isAgreeFriendRequest';
			modalMessage.value = '是否同意好友申请';
		} else if (type == '2') {
			modalMessageType.value = 'isRefuseFriendRequest';
			modalMessage.value = '是否拒绝好友申请';
		}
		tcgModalPopup.value.open();
	}

	provide('handleFriendRequest', handleFriendRequest);
</script>

<style scoped lang="scss">
	.visibility-hidden {
		visibility: hidden;
	}

	.container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		// 设置背景图片 平铺
		background-image: url("@/static/images/yellow_bg.png");
		background-repeat: repeat;

		.fight {
			position: relative;
			height: 184rpx;
			margin: 0 57.5rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.room-owner-ready {
				// flex: 1;
				width: 50%;
				height: 138rpx;
				// 背景渐变色
				background: linear-gradient(90deg, rgba(165, 214, 63, 1) 0%, rgba(165, 214, 63, 0) 100%);
				border-radius: 108rpx 0rpx 0rpx 108rpx;
				position: relative;
				// visibility: visible;

				.mark {
					position: absolute;
					width: 40rpx;
					height: 40rpx;
					top: 50%;
					left: 37rpx;
					transform: translate(0, -50%);
				}
			}

			.room-player-ready {
				// flex: 1;
				width: 50%;
				height: 138rpx;
				// 背景渐变色
				background: linear-gradient(90deg, rgba(165, 214, 63, 0) 0%, rgba(165, 214, 63, 1) 100%);
				border-radius: 0rpx 108rpx 108rpx 0rpx;
				position: relative;

				.mark {
					position: absolute;
					width: 40rpx;
					height: 40rpx;
					top: 50%;
					right: 37rpx;
					transform: translate(0, -50%);
				}
			}

			.start-fight-image {
				position: absolute;
				width: 352rpx;
				height: 184rpx;
				top: 0;
				left: 50%;
				transform: translate(-50%);
			}

		}
	}
</style>