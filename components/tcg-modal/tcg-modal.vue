<template>
	<view class="container">
		<view class="content-bg">
			<view class="content">
				<view class="message">
					<uv-text size="35rpx" :text="props.message"></uv-text>
				</view>
			</view>
		</view>
		<uv-gap height="17rpx" bgColor="white"></uv-gap>
		<view v-if="props.type == 1">
			<view class="cancel-confirm">
				<view class="cancel">
					<uv-button :customTextStyle="cancelBtnTextStyle" :custom-style="cancelBtnStyle" color="#fff"
						text="取消" @click="handleCancelClick"></uv-button>
				</view>
				<view class="confirm">
					<uv-button :customTextStyle="confirmBtnTextStyle" :custom-style="confirmBtnStyle" color="#fff"
						text="确定" @click="handleConfirmClick"></uv-button>
				</view>
			</view>
		</view>

		<view v-else-if="props.type == 2">
			<view class="confirm">
				<uv-button :customTextStyle="confirmBtnTextStyle" :custom-style="singleConfirmBtnStyle" color="#fff"
					text="确定" @click="handleConfirmClick"></uv-button>
			</view>
		</view>

	</view>
</template>

<script setup lang="ts">
	const cancelBtnStyle = {
		width: '180rpx',
		height: '65rpx',
		color: 'black',
		backgroundColor: 'white',
		borderRadius: '28rpx',
	};
	const cancelBtnTextStyle = {
		fontSize: '42rpx',

	};
	const confirmBtnStyle = {
		width: '180rpx',
		height: '65rpx',
		color: 'white',
		borderColor: '#E31C26',
		backgroundColor: '#E31C26',
		borderRadius: '28rpx',
	};
	const singleConfirmBtnStyle = {
		width: '424rpx',
		height: '65rpx',
		color: 'white',
		borderColor: '#E31C26',
		backgroundColor: '#E31C26',
		borderRadius: '28rpx',
	};
	const confirmBtnTextStyle = {
		fontSize: '42rpx',
	};
	/*
		message消息内容
		type 1: 确定和取消按钮 2: 只有确定按钮
		messageType 点击确定后返回给调用者的值
	*/
	const props = defineProps({
		message: { type: String, required: true },
		type: { type: Number, required: false, default: 1 },
		messageType: { type: String, required: true },
	})
	const emit = defineEmits(['confirm', 'cancel'])
	const handleCancelClick = () => {
		emit('cancel', props.messageType)
	}
	const handleConfirmClick = () => {
		emit('confirm', props.messageType)
	}
</script>

<style scoped lang="scss">
	$width: 550rpx;

	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		overflow: hidden;
		font-family: DeYiHei;

		.content-bg {
			background-color: rgba(56, 56, 56, 1);
			border-radius: 28rpx 49rpx 66rpx 49rpx;
			width: $width;
			min-height: 190rpx;
			display: flex;

			.content {
				flex: 1;
				width: 0;
				margin: 12rpx 17rpx 26rpx 9rpx;
				border-radius: 40rpx 44rpx 66rpx 49rpx;
				background-color: white;
				display: flex;
				justify-content: center;
				align-items: center;

				.message {
					margin: 55rpx 25rpx;
				}
			}
		}

		.cancel-confirm {
			width: $width;
			display: flex;
			justify-content: space-evenly;
			margin: 0 35rpx;
		}

	}
</style>