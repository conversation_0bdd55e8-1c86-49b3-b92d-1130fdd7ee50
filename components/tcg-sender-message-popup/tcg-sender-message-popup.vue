<template>
	<view class="container">
		<uv-gap height="35rpx" bgColor="#383838"></uv-gap>

		<view style="margin-left: 35rpx; margin-right: 35rpx;">
			<uv-text color="white" size="28rpx" :lines="1"
				:text="`发送给：${props.sendFriendItem.detail.nickName}（${props.sendFriendItem.detail.uid}）`"></uv-text>
		</view>
		<uv-gap height="35rpx" bgColor="#383838"></uv-gap>
		<view class="white-container">
			<uv-gap height="35rpx" bgColor="white"></uv-gap>
			<view class="textarea">
				<uv-textarea v-model="messageText" cursorSpacing="20" maxlength="500" height="100%"
					:customStyle="textareaStyle" placeholder="请输入想要发送的内容"></uv-textarea>
			</view>
			<uv-gap height="35rpx" bgColor="white"></uv-gap>
			<view class="cancel-send">
				<view class="cancel">
					<uv-button :customTextStyle="cancelBtnTextStyle" :custom-style="cancelBtnStyle" color="#fff"
						text="取消" @click="handleCloseBtnClick"></uv-button>
				</view>
				<view class="send" style="margin-left: 25rpx;" @click="handleSendMessage">
					<uv-button :customTextStyle="sendBtnTextStyle" :custom-style="sendBtnStyle" color="#fff"
						text="发送"></uv-button>
				</view>
			</view>
			<uv-gap height="35rpx" bgColor="white"></uv-gap>
		</view>
	</view>
</template>

<script setup lang="ts">
	import type { FriendItem } from '@/types/friend.d.ts';
	import { sendMessageAPI } from '@/services/message';
	import { ref, getCurrentInstance } from 'vue';
	const {
		ctx
	} : any = getCurrentInstance();

	const textareaStyle = {
		backgroundColor: "#E5E5E5",
		borderRadius: "37rpx",
		height: "100%",
	}
	const cancelBtnStyle = {
		color: 'black',
	};
	const cancelBtnTextStyle = {
		fontSize: '35rpx',

	};
	const sendBtnStyle = {
		width: '237rpx',
		height: '87rpx',
		color: 'white',
		backgroundColor: '#E31C26',
		borderRadius: '28rpx',
	};
	const sendBtnTextStyle = {
		fontSize: '49rpx',
	};
	const props = defineProps<{ sendFriendItem : FriendItem }>()
	const emit = defineEmits(["closeBtnClick"]);
	function handleCloseBtnClick() {
		emit("closeBtnClick");
	}
	let messageText = ref('');
	const handleSendMessage = async () => {
		if (ctx.$uv.test.empty(messageText.value)) {
			uni.showToast({
				title: '请输入发送内容',
				icon: 'none',
			});
			return
		}
		console.log(props.sendFriendItem.detail.uid);
		sendMessageAPI(props.sendFriendItem.detail.uid!, messageText.value).then(() => {
			uni.showToast({
				title: '发送成功',
				icon: 'none',
			});
			handleCloseBtnClick();
		})
	}
</script>

<style scoped lang="scss">
	.container {
		background-color: $color-383838;
		width: 750rpx;
		height: calc(100vh * 2.5 / 5);
		// 元素使用相对定位
		position: relative;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		font-family: AlimamaShuHeiTi;


		.white-container {
			overflow: hidden;
			background-color: white;
			flex: 1;
			height: 0;
			width: 100%;
			border-radius: 52rpx 52rpx 0rpx 0rpx;
			display: flex;
			flex-direction: column;

			.textarea {
				flex: 1;
				height: 0;
				margin: 0 35rpx;
			}

			.cancel-send {
				display: flex;
				justify-content: flex-end;
				margin: 0 35rpx;
				font-family: DeYiHei;
			}
		}
	}
</style>