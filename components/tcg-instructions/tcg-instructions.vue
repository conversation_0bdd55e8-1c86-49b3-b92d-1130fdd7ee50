<template>
	<view>
		<uv-overlay :show="props.isShowTcgInstructions" :opacity="1" z-index="99997">
			<view class="swiper-container">
				<!-- <uv-swiper :autoplay="false" :list="list" height="100%" imgMode="aspectFit" bgColor="white" radius="0"
					indicator indicatorMode="line" indicatorInactiveColor="red"
					indicatorActiveColor="black"></uv-swiper> -->
				<uv-swiper :autoplay="false" :list="list" height="100%" imgMode="aspectFit" bgColor="white" radius="0"
					@click="handleClickSwiper" :current="currentIndex" @change="onSwiperChange"></uv-swiper>
			</view>
		</uv-overlay>
	</view>
</template>

<script setup lang="ts">
	import { ref } from 'vue';
	const props = defineProps({
		isShowTcgInstructions: { type: Boolean, required: true }
	})
	const currentIndex = ref(0);
	const list = ref([
		'https://www.duelchannel.com/min-api/file/resource/guidance/guidance_1.png',
		'https://www.duelchannel.com/min-api/file/resource/guidance/guidance_2.png',
		'https://www.duelchannel.com/min-api/file/resource/guidance/guidance_3.png',
		'https://www.duelchannel.com/min-api/file/resource/guidance/guidance_4.png',
		'https://www.duelchannel.com/min-api/file/resource/guidance/guidance_5.png',
	])
	const onSwiperChange = (e : any) => {
		currentIndex.value = e.current
	}

	const emit = defineEmits(["onInstructionsClick",]);

	const handleClickSwiper = (index : number) => {
		// 点击查看下一页
		if (index === list.value.length - 1) {
			emit("onInstructionsClick");
		} else {
			currentIndex.value = index + 1;
		}
	}
</script>

<style scoped lang="scss">
	.swiper-container {
		height: 100%;
		width: 100%;
	}
</style>