<template>
	<view class="container">
		<view class="tcg-swiper">
			<!-- <uv-swiper imgMode="aspectFit" bgColor="rgba(255, 255, 255, 1)" height="200rpx"
				:list="useGamesStore().gameList" previousMargin="180rpx" nextMargin="180rpx" :autoplay="false"
				radius="5" keyName="img" circular :current="swiperSelectedIndex" @change="changeSwiper">
			</uv-swiper> -->

			<swiper class="swiper-content" :autoplay="false" :current="swiperSelectedIndex" :circular="true"
				previous-margin="200rpx" next-margin="200rpx" @change="changeSwiper">
				<swiper-item v-for="(item, index) in useGamesStore().gameList" :key="index"
					@click="onClickSwiperItem(index)">
					<image mode="aspectFit" :src="item.img" class="slide-image"
						:class="swiperSelectedIndex == index ? 'active' : ''">
					</image>
				</swiper-item>
			</swiper>

			<view class="liked" @click="handleLikeClick">
				<uv-icon :name="isLiked ? 'star-fill' : 'star'" size="35rpx"
					:color="isLiked ? '#F4EA29' : 'black'"></uv-icon>
			</view>
		</view>

		<scroll-view class="scroll-picker" scroll-x="true" scroll-with-animation="true" :scroll-left="scrollLeft"
			@scroll="scroll">
			<view class="picker-container" @touchstart="pickerTouchstart" @touchmove="pickerTouchmove"
				@touchend="pickerTouchend" @touchcancel="pickerTouchcancel">
				<view class="before-picker-view" :style="beforeStyle"></view>
				<view v-for="(item, index) in pickerItems" :key="item.typeId"
					:class="['picker-item', { active: pickerSelectedIndex === index }]" @click="selectItem(index)">
					{{ item.typeName }}
				</view>
				<view :style="afterStyle"></view>
			</view>
		</scroll-view>

		<view class="cancel-confirm">
			<view class="cancel">
				<uv-button :customTextStyle="cancelBtnTextStyle" :custom-style="cancelBtnStyle" color="#fff" text="取消"
					@click="handleCancelClick"></uv-button>
			</view>
			<view class="confirm">
				<uv-button :customTextStyle="confirmBtnTextStyle" :custom-style="confirmBtnStyle" color="#fff" text="确定"
					@click="handleConfirmClick"></uv-button>
			</view>
		</view>

	</view>
</template>

<script setup lang="ts">
	import {
		ref,
		getCurrentInstance,
		reactive,
		onMounted,
		nextTick,
		computed,
	} from 'vue';
	import { useGamesStore } from '@/stores/modules/games';
	import { UserLikeVO } from '@/types/member.d.ts';
	import { GameFormatDetail } from '@/types/game.d.ts';
	import { tcgPxToRpx } from '@/utils/index';
	import { useMemberStore } from '@/stores/index';
	import { updateMemberProfileAPI } from '@/services/profile';

	const cancelBtnStyle = {
		height: '100rpx',
		color: 'black',
		borderColor: 'white',
		backgroundColor: 'white',
		borderRadius: '0 0 0 10rpx',
	};
	const cancelBtnTextStyle = {
		fontSize: '32rpx',

	};
	const confirmBtnStyle = {
		height: '100rpx',
		color: 'white',
		borderColor: '#A5D63F',
		backgroundColor: '#A5D63F',
		borderRadius: '0 0 10rpx 0',
	};
	const confirmBtnTextStyle = {
		fontSize: '32rpx',
	};
	const props = defineProps<{ pageGameInfo : UserLikeVO }>()

	const {
		ctx
	} : any = getCurrentInstance();

	const beforeStyle = reactive({
		width: '0rpx',
		flexShrink: 0,
	});
	const afterStyle = reactive({
		width: '0rpx',
		flexShrink: 0,
	});

	const swiperSelectedIndex = ref(0); // 当前选中项的索引

	const pickerItems = reactive<GameFormatDetail[]>([]) // 选择项数据
	const pickerSelectedIndex = ref(); // 当前选中项的索引
	const scrollLeft = ref(0); // 滚动视图的左边距
	const contentScrollW = ref(0)

	// 判断是通过手指滚动还是点击item触发的scroll滚动
	let interactionType = 'click'
	const selectItem = async (index : number) => {
		interactionType = 'click'
		pickerSelectedIndex.value = index;
		console.log(pickerItems[index].left);
		// 在这里添加额外的逻辑，比如触发事件或更新数据
		scrollLeft.value = pickerItems[index].left - contentScrollW.value / 2 + pickerItems[index].width / 2;
	};
	onMounted(async () => {
		// 20毫秒后再做
		await new Promise((resolve) => {
			setTimeout(() => {
				resolve('')
			}, 20);
		})

		// 循环useGamesStore().gameList
		for (let i = 0; i < useGamesStore().gameList.length; i++) {
			if (useGamesStore().gameList[i].gameId == props.pageGameInfo.gameId) {
				swiperSelectedIndex.value = i
				pickerItems.length = 0
				pickerItems.push(...useGamesStore().gameList[i].types)
			}
		}

		let pickerIndex = 0
		for (let i = 0; i < pickerItems.length; i++) {
			if (pickerItems[i].typeId == props.pageGameInfo.gameTypeId) {
				pickerIndex = i
			}
		}

		await nextTick()

		const pickerItemRes = await ctx.$uv.getRect('.picker-item', true)
		const screenWidth = uni.getSystemInfoSync().screenWidth
		if (pickerItemRes.length > 0) {
			const firstItemWidth = pickerItemRes[0].width;
			const beforeStyleWidth = (screenWidth - firstItemWidth) / 2
			const beforeStyleWidthRpx = tcgPxToRpx(beforeStyleWidth)
			beforeStyle.width = afterStyle.width = `${beforeStyleWidthRpx}rpx`

			console.log(pickerItemRes);

			for (let i = 0; i < pickerItemRes.length; i++) {
				//  scroll-view 子元素组件距离左边栏的距离
				pickerItems[i].left = pickerItemRes[i].left + beforeStyleWidth;
				//  scroll-view 子元素组件宽度
				pickerItems[i].width = pickerItemRes[i].width
			}
		}
		const scrollPickerRes = await ctx.$uv.getRect('.scroll-picker')
		contentScrollW.value = scrollPickerRes.width

		selectItem(pickerIndex)

	})


	// 界面上最终显示的游戏信息
	const isLiked = computed(() => {
		if (isNaN(pickerSelectedIndex.value)) {
			return false
		}
		const gameId = useGamesStore().gameList[swiperSelectedIndex.value].gameId
		const gameTypeId = pickerItems[pickerSelectedIndex.value].typeId

		if (gameId == useMemberStore().profile.gameId && gameTypeId == useMemberStore().profile.gameTypeId) {
			return true
		}
		return false
	})

	const handleLikeClick = () => {
		const gameId = useGamesStore().gameList[swiperSelectedIndex.value].gameId
		const gameName = useGamesStore().gameList[swiperSelectedIndex.value].gameName
		const img = useGamesStore().gameList[swiperSelectedIndex.value].img
		const gameTypeName = pickerItems[pickerSelectedIndex.value].typeName
		const gameTypeId = pickerItems[pickerSelectedIndex.value].typeId
		uni.showLoading({
			title: ''
		});
		updateMemberProfileAPI({
			gameId: gameId,
			gameTypeId: gameTypeId
		}).then(() => {
			useMemberStore().profile.gameId = gameId;
			useMemberStore().profile.gameName = gameName;
			useMemberStore().profile.img = img;
			useMemberStore().profile.gameTypeName = gameTypeName;
			useMemberStore().profile.gameTypeId = gameTypeId;
			uni.hideLoading();
		})
	}


	const timer = ref(null)
	let isTouchEnd = false
	const scrollEndDetail = ref()
	const pickerTouchstart = (e) => {
		isTouchEnd = false
	}
	const pickerTouchmove = (e) => {
		interactionType = 'move'
		isTouchEnd = false
	}

	const pickerTouchend = (e) => {
		isTouchEnd = true
	}
	const pickerTouchcancel = (e) => {
		isTouchEnd = true
	}

	const scroll = (e) => {
		if (timer.value) {
			clearTimeout(timer.value)
		}
		scrollEndDetail.value = e.detail
		timer.value = setTimeout(pickerScrollEnd, 100);
	}

	const pickerScrollEnd = () => {
		clearTimeout(timer.value)
		timer.value = null

		console.log(interactionType);

		if (
			interactionType == 'click') {
			return
		}
		const scrollViewLeft = scrollEndDetail.value.scrollLeft + uni.getSystemInfoSync().screenWidth / 2
		console.log(scrollViewLeft)
		let index = 0
		for (let i = 0; i < pickerItems.length; i++) {
			if (pickerItems[i].left < scrollViewLeft) {
				index = i
			}
		}
		pickerSelectedIndex.value = index;
		scrollLeft.value = pickerItems[index].left - contentScrollW.value / 2 + pickerItems[index].width / 2;
	}

	const changeSwiper = async (e) => {
		selectItem(0)
		swiperSelectedIndex.value = e.detail.current
		const itemWidth = pickerItems[0].width
		pickerItems.length = 0
		pickerItems.push(...useGamesStore().gameList[e.detail.current].types)
		const screenWidth = uni.getSystemInfoSync().screenWidth
		const beforeStyleWidth = (screenWidth - itemWidth) / 2
		for (let i = 0; i < pickerItems.length; i++) {
			//  scroll-view 子元素组件距离左边栏的距离
			pickerItems[i].left = beforeStyleWidth + itemWidth * i;
			//  scroll-view 子元素组件宽度
			pickerItems[i].width = itemWidth
		}

		selectItem(0)


		// swiperSelectedIndex.value = e.current
		// console.log(pickerItems[0].width);
		// pickerItems.length = 0
		// pickerItems.push(...useGamesStore().gameList[e.current].types)

		// pickerSelectedIndex.value = -1;
		// await nextTick()
		// const beforePickerViewRes = await ctx.$uv.getRect('.before-picker-view')
		// const pickerItemRes = await ctx.$uv.getRect('.picker-item', true)
		// console.log(pickerItemRes);
		// if (pickerItemRes.length > 0) {
		// 	for (let i = 0; i < pickerItemRes.length; i++) {
		// 		//  scroll-view 子元素组件距离左边栏的距离
		// 		pickerItems[i].left = beforePickerViewRes.width + pickerItemRes[i].width * i;
		// 		//  scroll-view 子元素组件宽度
		// 		pickerItems[i].width = pickerItemRes[i].width
		// 	}
		// }
		// await nextTick()
		// selectItem(0)

	}

	const onClickSwiperItem = (index : number) => {
		swiperSelectedIndex.value = index
	}

	const emit = defineEmits(["cancel", "confirm"]);


	const handleCancelClick = () => {
		emit("cancel");
	}
	const handleConfirmClick = () => {
		const chooseGame = {
			gameId: useGamesStore().gameList[swiperSelectedIndex.value].gameId,
			gameName: useGamesStore().gameList[swiperSelectedIndex.value].gameName,
			img: useGamesStore().gameList[swiperSelectedIndex.value].img,
			gameTypeId: pickerItems[pickerSelectedIndex.value].typeId,
			gameTypeName: pickerItems[pickerSelectedIndex.value].typeName,
		}
		console.log(chooseGame);
		emit("confirm", chooseGame);
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		width: 100vw;
		// align-items: center;
		// overflow: hidden;
		// font-family: DeYiHei;

		.tcg-swiper {
			border-radius: 10rpx 10rpx 0 0;
			padding: 65rpx 0;
			background-color: rgba(255, 255, 255, 1);
			position: relative;

			.liked {
				position: absolute;
				transform: translate(-50%);
				left: 50%;
				bottom: 15rpx;
			}

			.swiper-content {
				height: 155.5rpx;

				.slide-image {
					height: 100%;
					transform: scale(0.8);
					opacity: 0.5;
					width: 350rpx;
				}

				.active {
					height: 100%;
					transform: scale(1);
					opacity: 1;
					transition: transform 0.3s;
				}
			}
		}

		.scroll-picker {
			width: 100%;
			/* 设置滚动视图的宽度 */
			overflow-x: auto;
			background-color: rgba(56, 56, 56, 0.82);

			.picker-container {
				display: flex;
				width: 100vw;
			}

			.picker-item {
				color: rgba(255, 255, 255, 0.69);
				padding: 0 40rpx;
				white-space: nowrap;
				font-size: 21rpx;
				// background-color: yellow;
				line-height: 94rpx;

				&.active {
					color: white;
					/* 选中状态的样式 */
					font-weight: bold;
					/* font-size: 28rpx; */
					transform: scale(1.3333);
					transition: transform 0.3s;
				}
			}

			// .picker-item.active {
			// 	color: red;
			// 	/* 选中状态的样式 */
			// 	font-weight: bold;
			// 	/* font-size: 28rpx; */
			// 	transform: scale(1.3333);
			// 	transition: transform 0.3s;
			// }
		}

		.cancel-confirm {
			width: 100%;
			display: flex;
			justify-content: space-between;

			.cancel {
				width: 50%;
			}

			.confirm {
				width: 50%;
			}
		}
	}
</style>