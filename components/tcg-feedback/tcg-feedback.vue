<template>
	<view class="container">
		<view style="margin-left: 59rpx; margin-top: 44rpx;  font-family: AlimamaShuHeiTi;">
			<uv-icon name="file-text" size="66rpx" color="black" label="反馈" label-size="48rpx"
				label-color="black"></uv-icon>
		</view>
		<uv-gap height="25rpx" bgColor="white"></uv-gap>

		<view class="search">
			<uv-input v-model="feedbackTitle" placeholder="请输入反馈标题" fontSize="35rpx"
				:customStyle="searchStyle"></uv-input>
		</view>

		<uv-gap height="25rpx" bgColor="white"></uv-gap>
		<view class="textarea">
			<uv-textarea v-model="feedbackMessage" cursorSpacing="20" maxlength="500" height="100%"
				:customStyle="textareaStyle" placeholder="请输入想要发送的内容" :textStyle="textareaTextStyle"></uv-textarea>
		</view>
		<uv-gap height="25rpx" bgColor="white"></uv-gap>

		<view class="commit">
			<uv-button :customTextStyle="sendBtnTextStyle" :custom-style="sendBtnStyle" color="#fff" text="提交"
				@click="handleCommitFeedbackClick"></uv-button>
		</view>
		<uv-gap height="125rpx" bgColor="white"></uv-gap>

	</view>
</template>

<script setup lang="ts">
	import {
		getCurrentInstance,
		ref,
	} from 'vue';
	import { sendFeedbackAPI } from '@/services/message';

	const {
		ctx
	} : any = getCurrentInstance();
	const searchStyle = {
		borderRadius: "28rpx 54rpx 28rpx 28rpx",
		height: "108rpx",
		backgroundColor: "#E5E5E5"
	}
	const textareaStyle = {
		backgroundColor: "#E5E5E5",
		borderRadius: "28rpx",
		height: "100%",
		paddingLeft: "13px",
		paddingRight: "13px",
	}
	const textareaTextStyle = {
		fontSize: "35rpx",
		color: "#303133",
	}

	const sendBtnStyle = {
		width: '237rpx',
		height: '87rpx',
		color: 'white',
		backgroundColor: '#E31C26',
		borderRadius: '28rpx',
	};
	const sendBtnTextStyle = {
		fontSize: '49rpx',
	};

	let feedbackTitle = ref("");
	let feedbackMessage = ref("");
	const emit = defineEmits(["onCloseFeedbackPopup"]);
	const handleCommitFeedbackClick = () => {
		if (ctx.$uv.test.empty(feedbackTitle.value)) {
			uni.showToast({
				title: '请输入反馈标题',
				icon: 'none',
			});
			return
		}
		if (ctx.$uv.test.empty(feedbackMessage.value)) {
			uni.showToast({
				title: '请输入反馈内容',
				icon: 'none',
			});
			return
		}
		uni.showLoading({
			title: ''
		})
		sendFeedbackAPI(feedbackTitle.value, feedbackMessage.value).then(res => {
			uni.hideLoading()
			uni.showToast({
				title: '提交成功',
				icon: 'none',
			});
			setTimeout(() => {
				emit("onCloseFeedbackPopup");
			}, 1000);
		})
	}
</script>

<style scoped lang="scss">
	$margin: 25rpx;

	.container {
		display: flex;
		flex-direction: column;
		height: 100%;

		.search {
			margin: 0 $margin;
		}

		.textarea {
			margin: 0 $margin;
			flex: 1;
			height: 0;
		}

		.commit {
			margin: 0 $margin;
			align-self: flex-end;
		}
	}
</style>