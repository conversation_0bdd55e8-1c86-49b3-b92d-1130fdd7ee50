<template>
	<view class="container">
		<uv-gap height="25rpx" bgColor="#383838"></uv-gap>
		<image class="close-btn" @click="handleCloseBtnClick" src="../../static/images/icon_popup_close.png" mode="">
		</image>
		<view class="white-container">
			<slot></slot>
		</view>
	</view>
</template>

<script setup lang="ts">
	const emit = defineEmits(["closeBtnClick"]);
	function handleCloseBtnClick() {
		emit("closeBtnClick");
	}
</script>

<style scoped lang="scss">
	.container {
		background-color: $color-383838;
		width: 750rpx;
		height: calc(100vh * 3 / 4);
		// 元素使用相对定位
		position: relative;
		overflow: hidden;

		.close-btn {
			position: absolute;
			top: 44rpx;
			right: 49rpx;
			width: 47rpx;
			height: 43rpx;
		}

		.white-container {
			overflow: hidden;
			background-color: white;
			height: calc(100% - 25rpx);
			width: 700rpx;
			margin: 0rpx 25rpx;
			border-radius: 80rpx 260rpx 0rpx 0rpx;
			display: flex;
			flex-direction: column;
		}
	}
</style>