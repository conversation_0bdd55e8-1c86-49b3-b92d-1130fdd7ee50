<template>
	<view v-show="isReportDetail">
		<view class="report-detail-container">
			<view v-for="(item, index) in reportContentList" :key="index" class="report-list">
				<template v-if="index != 0">
					<uv-gap height="12rpx"></uv-gap>
				</template>
				<view class="reportStyle"
					:class="{ 'reportSelectedStyle': item.selected, 'reportUnselectedStyle': !item.selected }"
					@click="toggleSelection(index)">
					{{ item.title }}
				</view>
			</view>
			<uv-gap height="12rpx"></uv-gap>
			<view class="other-report-container">
				<view class="other-report">
					其他
				</view>
				<view class="input-container">
					<view class="input">
						<uv-textarea cursorSpacing="20" maxlength="500" placeholderStyle="color: black;"
							:textStyle="reportTextStyle" :customStyle="reportInputStyle" v-model="reportInputValue"
							placeholder="请输入举报内容"></uv-textarea>
					</view>
				</view>
			</view>
			<uv-gap height="80rpx"></uv-gap>
			<view class="back-commit">
				<text class="back" @click="handleBackClick">返回</text>
				<text class="commit" @click="handleCommitClick">提交</text>
			</view>
		</view>
	</view>

	<view v-show="!isReportDetail">
		<view class="report-masking-container">
			<view class="report-container" @click="onOpenReportDetail">
				<view class="report-icon">
					<uv-icon name="warning" size="92rpx" color="black"></uv-icon>
				</view>
				<text class="report-text">举报</text>
				<view class="round-point round-point1"></view>
				<view class="round-point round-point2"></view>
				<view class="round-point round-point3"></view>
				<view class="round-point round-point4"></view>
			</view>
			<uv-gap height="28rpx"></uv-gap>
			<view class="masking-container" @click="handleMaskingClick">拉黑</view>
			<uv-gap height="72rpx"></uv-gap>
			<view class="close" @click="handleCloseClick">
				<text class="close-icon">×</text>
				<text class="cancle-text">取消</text>
			</view>
		</view>
	</view>


</template>

<script setup lang="ts">
	import { ref, reactive } from 'vue';
	import { empty } from '@/uni_modules/uv-ui-tools/libs/function/test.js';
	import { ReportParams } from '@/types/Report.d.ts';

	const isReportDetail = ref(false);
	const emit = defineEmits(['onCloseReportPopup', 'onCommitReportClick', 'onMaskingClick']);
	const handleCloseClick = () => {
		emit('onCloseReportPopup');
	}
	const handleMaskingClick = () => {
		emit('onMaskingClick');
	}

	const onOpenReportDetail = () => {
		isReportDetail.value = true;
	}

	const reportContentList = reactive([
		{ selected: false, title: '色情、低俗、违法违规', id: '1' },
		{ selected: false, title: '赌博、诈骗', id: '2' },
		{ selected: false, title: '人身攻击、侵犯隐私', id: '3' },
		{ selected: false, title: '人身威胁、诱拐', id: '4' },
	]);

	const toggleSelection = (index : number) => {
		reportContentList[index].selected = !reportContentList[index].selected;
	};
	let reportInputValue = ref('');
	const reportInputStyle = {
		backgroundColor: "rgba(128, 128, 128, 0.53)",
		borderRadius: "17rpx",
		height: "100%",
		width: "100%",
	}
	const reportTextStyle = {
		fontSize: "28rpx",
		color: "black"
	}
	const handleBackClick = () => {
		isReportDetail.value = false;
	}
	const handleCommitClick = () => {
		//判断 reportContentList里面selected没有一个为true并且reportInputValue为空字符串就提示至少选择或者输入字符串
		let flag = false;
		let reportContentId = '';

		reportContentList.forEach((item) => {
			if (item.selected) {
				flag = true;
				reportContentId += item.id + ',';
			}
		});

		if (!flag && empty(reportInputValue.value)) {
			uni.showToast({
				title: '请至少选择一项或输入举报内容',
				icon: 'none',
			});
			return
		}
		reportContentId = reportContentId.substring(0, reportContentId.length - 1);
		if (!empty(reportInputValue.value)) {
			reportContentId += ',10';
		}

		const reportParams : ReportParams = {
			typeId: reportContentId,
			message: reportInputValue.value,
		};
		emit('onCommitReportClick', reportParams);
	}
</script>

<style scoped lang="scss">
	.report-detail-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		font-family: AlimamaShuHeiTi;

		.report-list {
			display: flex;
			flex-direction: column;
			align-items: center;

			.reportStyle {
				width: 494rpx;
				height: 96rpx;
				line-height: 96rpx;
				font-size: 28rpx;
				border-radius: 28rpx;
				text-align: center;
			}

			.reportUnselectedStyle {
				/* 未选中view的背景颜色 */
				background-color: white;
			}

			.reportSelectedStyle {
				/* 选中view的背景颜色 */
				background-color: #FDDE40;
			}
		}

		.other-report-container {
			display: flex;
			flex-direction: column;
			align-items: center;

			.other-report {
				width: 494rpx;
				height: 96rpx;
				line-height: 96rpx;
				font-size: 28rpx;
				border-radius: 28rpx;
				text-align: center;
				background-color: white;
				z-index: 1;
			}

			.input-container {
				margin-top: -28rpx;
				width: 494rpx;
				height: 230rpx;
				border-radius: 0rpx 0rpx 28rpx 28rpx;
				background-color: rgba(204, 204, 204, 1);
				display: flex;
				flex-direction: column;
				align-items: center;

				.input {
					margin-top: 54rpx;
					width: 420rpx;
					height: 150rpx;
				}
			}
		}

		.back-commit {
			display: flex;
			flex-direction: row;
			justify-content: space-around;
			align-items: center;
			width: 494rpx;

			.back {
				font-size: 42rpx;
				color: rgba(212, 48, 48, 1);
			}

			.commit {
				font-size: 42rpx;
				color: rgba(67, 207, 124, 1);
			}
		}
	}

	.report-masking-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		overflow: hidden;
		font-family: AlimamaShuHeiTi;

		.report-container {
			overflow: hidden;
			position: relative;
			height: 176rpx;
			width: 494rpx;
			background-color: white;
			border-radius: 37rpx;
			// 圆角

			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;

			.report-text {
				font-size: 35rpx;
				color: black;
			}

			.round-point {
				position: absolute;
				height: 16rpx;
				width: 16rpx;
				border-radius: 50%;
				background-color: rgba(212, 48, 48, 1);
			}

			.round-point1 {
				top: 19rpx;
				left: 28rpx;
			}

			.round-point2 {
				top: 19rpx;
				right: 28rpx;
			}

			.round-point3 {
				bottom: 19rpx;
				right: 28rpx;
			}

			.round-point4 {
				bottom: 19rpx;
				left: 28rpx;
			}
		}

		.masking-container {
			height: 80rpx;
			width: 494rpx;
			// background-color: rgba(204, 204, 204, 1);
			background-color: white;
			border-radius: 28rpx;
			line-height: 80rpx;
			text-align: center;
			font-size: 35rpx;
			color: black;
		}

		.close {
			display: flex;
			flex-direction: column;
			align-items: center;

			.close-icon {
				font-size: 35rpx;
				color: rgba(212, 48, 48, 1);
			}

			.cancle-text {
				font-size: 35rpx;
				color: rgba(212, 48, 48, 1);
			}
		}
	}
</style>