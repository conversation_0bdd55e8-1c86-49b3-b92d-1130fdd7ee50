<template>
	<view class="container">
		<view style="margin-left: 59rpx; margin-top: 44rpx; font-family: AlimamaShuHeiTi;">
			<uv-icon name="search" size="66rpx" color="black" label="搜索" label-size="48rpx"
				label-color="black"></uv-icon>
		</view>
		<uv-gap height="25rpx" bgColor="white"></uv-gap>

		<view class="search">
			<uv-input type="number" v-model="searchText" placeholder="请输入玩家UID或房间号"
				placeholderStyle="color: rgba(0, 0, 0, 0.5);" fontSize="35rpx" :customStyle="searchStyle"
				@blur="onSearchBlur"></uv-input>
		</view>

		<view class="list">

			<view v-if="searchResult.total > 0">
				<scroll-view scroll-y="true" lower-threshold="10rpx" :style="{height: listHeight + 'px'}">
					<uv-list>
						<uv-list-item v-for="item in searchResult.players" :key="item.uid">
							<uv-gap height="25rpx" bgColor="white"></uv-gap>
							<view class="friend-list-item">
								<view class="delete" @click="deleteFriend(item)">x</view>
								<view class="friend-list-item-left">
									<!-- 头像 -->
									<view class="avatar">
										<uv-avatar :src="item.avatar" size="45rpx" shape="circle"></uv-avatar>
										<view class="is-online" v-show="item.online === '0'"></view>
									</view>
									<!-- 昵称 -->
									<view class="nickname-container">
										<view class="nickname">
											<uv-text size="18rpx" :lines="1" :text="item.nickName"></uv-text>
										</view>

										<view class="lv">
											<view class="lv-name">LV</view>
											<view class="lv-number">{{ item.level }}</view>
										</view>
									</view>
									<view class="game">
										<uv-image :src="item.img" height="40rpx" width="70rpx"
											mode="aspectFit"></uv-image>
									</view>
								</view>
								<view class="friend-list-item-right" @click="addFriend(item)">
									<uv-image
										src="https://img.js.design/assets/img/6561d1a8d0d412d9d9f3307b.png#c2cbbe3cdc166f579ee666a4f31d1739"
										height="31rpx" width="31rpx" mode="aspectFit"></uv-image>
								</view>
							</view>
						</uv-list-item>
					</uv-list>

					<uv-list>
						<uv-list-item v-for="item in searchResult.rooms" :key="item.rid">
							<uv-gap height="25rpx" bgColor="white"></uv-gap>
							<view class="room-item-container">
								<view class="delete" @click="deleteRoom">x</view>
								<view class="room-item">
									<view class="room-item-number">
										<view class="room-number">
											<uv-text size="22rpx" :lines="1" :text="`房间号：${item.rid}`"></uv-text>
										</view>
										<view class="room-status"
											style="display: flex; align-items: center;justify-content: center;">
											<uv-text size="18rpx" lineHeight="22rpx"
												:color="roomStatusTextColor(item.status)" :lines="1"
												:text="roomStatusText(item.status)"></uv-text>
										</view>

									</view>
									<view class="room-item-operation">
										<uv-button :icon="roomOperationIcon(item.status)"
											:customTextStyle="operationBtnTextStyle" :custom-style="operationBtnStyle"
											color="#fff" :text="roomOperationText(item.status)"
											@click="roomOperation(item)"></uv-button>
									</view>
								</view>
							</view>
						</uv-list-item>
					</uv-list>
				</scroll-view>
			</view>

			<view v-else class="empty">
				<uv-empty mode="search"></uv-empty>
			</view>

		</view>
	</view>
</template>

<script setup lang="ts">
	import {
		getCurrentInstance,
		ref,
		reactive,
		onMounted
	} from 'vue';
	import { searchPlayerOrRoomAPI } from '@/services/search';
	import { SearchListResult } from '@/types/search.d.ts';
	import { addFriendAPI } from '@/services/friend';
	import { ProfileDetail } from '@/types/member.d.ts';
	import { useMemberStore } from '@/stores/index';
	import { joinRoomAPI } from '@/services/room'
	import { RoomDetail } from '@/types/room.d.ts';

	const {
		ctx
	} : any = getCurrentInstance();

	const searchStyle = {
		borderRadius: "28rpx 54rpx 28rpx 28rpx",
		height: "108rpx",
		backgroundColor: "#E5E5E5"
	}
	let searchText = ref("");
	const onSearchBlur = (text : string) => {
		searchPlayerOrRoom(text)
	}

	const operationBtnStyle = {
		// height: '50rpx',
		color: '#383838',
	};
	const operationBtnTextStyle = {
		fontSize: '22rpx',
	};

	// 房间状态text
	const roomStatusText = (status : number) => {
		switch (status) {
			case 1:
				return "（准备中）";
			case 2:
				return "（房间已满）";
			case 3:
				return "（比赛中）";
			default:
				return "";
		}
	}

	// 房间状态textColor
	const roomStatusTextColor = (status : number) => {
		switch (status) {
			case 1:
				return "rgba(165, 214, 63, 1)";
			default:
				return "rgba(212, 48, 48, 1)";
		}
	}

	// 房间操作的icon
	const roomOperationIcon = (status : number) => {
		switch (status) {
			case 1:
				return "lock-open";
			case 2:
				return "reload"
			case 3:
				return "eye"
			default:
				return "";
		}
	}

	// 房间操作的text
	const roomOperationText = (status : number) => {
		switch (status) {
			case 1:
				return "进入房间";
			case 2:
				return "刷新"
			case 3:
				return "进入网页版观战"
			default:
				return "";
		}
	}
	const roomOperation = (item : RoomDetail) => {
		switch (item.status) {
			case 1:
				joinRoom(item);
				break;
			case 2:
				searchPlayerOrRoom(searchText.value)
				break;
			case 3:
				uni.showToast({
					title: '进入网页版观战',
					icon: 'none'
				})
				break;
			default:
				break;
		}
	}

	const joinRoom = async (item : RoomDetail) => {
		uni.showLoading({
			title: ''
		})
		joinRoomAPI(item.rid!, 2).then(res => {
			uni.hideLoading()
			let roomDetail = encodeURIComponent(JSON.stringify(res.data));
			ctx.$uv.route({
				url: '/pagesRoom/room/room',
				params: {
					roomDetail: roomDetail
				}
			})
		})
	}


	let listHeight = ref(0);
	onMounted(async () => {
		const dada = await ctx.$uv.getRect('.list');
		listHeight.value = dada.height;
		// searchPlayerOrRoom("");
	})

	const searchResult : SearchListResult = reactive<SearchListResult>({})
	const searchPlayerOrRoom = async (id : string) => {
		if (id.length < 3) {
			uni.showToast({
				title: '请输入至少3位数字',
				icon: 'none'
			})
			return;
		}
		if (id == useMemberStore().profile.uid) {
			uni.showToast({
				title: '不能搜索自己哦!',
				icon: 'none'
			})
			return;
		}
		uni.showLoading({
			title: ''
		})
		searchPlayerOrRoomAPI(id).then(res => {
			uni.hideLoading()
			Object.assign(searchResult, res.data);
		})
	}


	const addFriend = async (item : ProfileDetail) => {
		uni.showLoading({
			title: ''
		})
		addFriendAPI(item.uid!).then(res => {
			uni.hideLoading()
			uni.showToast({
				title: '已发送好友申请',
				icon: 'none'
			})
		})

	}
	const deleteFriend = async (item : ProfileDetail) => {
		// 从 searchResult.players 中删除 item
		const index = searchResult.players.findIndex((player) => {
			return player.uid === item.uid;
		})
		if (index !== -1) {
			searchResult.players.splice(index, 1);
		}
		searchText.value = "";
	}
	const deleteRoom = async () => {
		console.log("删除房间");
	}
</script>

<style scoped lang="scss">
	$margin: 25rpx;

	.container {
		display: flex;
		flex-direction: column;
		height: 100%;

		.search {
			margin: 0 $margin;
			font-family: DeYiHei;
		}

		.list {
			flex: 1;
			height: 0;

			.empty {
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.friend-list-item {

				background-color: $color-383838;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
				margin: 0 $margin;
				border-radius: 45rpx 28rpx 54rpx 10rpx;
				position: relative;

				.delete {
					position: absolute;
					left: 7rpx;
					bottom: 18rpx;
					font-size: 22rpx;
					width: 12rpx;
					height: 12rpx;
					color: white;
				}

				.friend-list-item-left {
					flex: 1;
					margin: 10rpx 0rpx 10rpx 5rpx;
					background-color: white;
					display: flex;
					align-items: center;
					border-radius: 39rpx;

					.avatar {
						// 相对定位
						position: relative;
						margin: 15rpx;

						.is-online {
							width: 16rpx;
							height: 16rpx;
							right: 0;
							bottom: 0;
							background-color: $color-B6FF1A;
							position: absolute;
							border-radius: 50%;
						}
					}

					.nickname-container {
						flex: 2;
						display: flex;
						justify-content: space-between;
						align-items: center;
						background-color: $color-EFEFEF;
						border-radius: 8rpx;
						height: 45rpx;
						padding: 0 10rpx 0 18rpx;

						.nickname {
							font-family: AlimamaShuHeiTi;
						}

						.lv {
							display: flex;
							background-color: $color-F7E439;
							border-radius: 8rpx 11rpx 14rpx 11rpx;

							.lv-name {
								font-weight: bold;
								background-color: $color-383838;
								color: white;
								padding: 0rpx 10rpx;
								font-size: 16rpx;
								border-radius: 8rpx 11rpx 14rpx 11rpx;
							}

							.lv-number {
								margin-left: 4rpx;
								margin-right: 10rpx;
								font-weight: bold;
								font-size: 16rpx;
							}
						}
					}

					.game {
						padding-left: 25rpx;
						padding-right: 25rpx;
					}

					.mode {
						text-align: center;
						font-size: 18rpx;
						flex: 1;
					}
				}

				.friend-list-item-right {
					margin: 0 35rpx 0 25rpx;
					font-size: 28rpx;
					font-weight: bold;
					color: white;
				}

			}


			.room-item-container {
				background-color: $color-383838;
				display: flex;
				flex-direction: column;
				margin: 0 $margin;
				border-radius: 21rpx 54rpx 28rpx 10rpx;
				position: relative;

				.delete {
					position: absolute;
					left: 8rpx;
					bottom: 20rpx;
					font-size: 22rpx;
					width: 12rpx;
					height: 12rpx;
					color: white;
				}

				.room-item {
					margin: 10rpx 15rpx 10rpx 5rpx;
					padding: 25rpx;
					background-color: white;
					display: flex;
					align-items: center;
					border-radius: 21rpx 37rpx 30rpx 45rpx;
					justify-content: space-between;

					.room-item-number {
						display: flex;
						flex: 1;
						width: 0;

						.room-number {
							font-family: YouSheBiaoTiHei;
						}

						.room-status {
							font-family: AlimamaShuHeiTi;
						}

					}

					.room-item-operation {
						font-family: DeYiHei;
					}
				}
			}
		}
	}
</style>