<template>
	<view class="container">
		<view style="margin-left: 59rpx; margin-top: 44rpx; font-family: AlimamaShuHeiTi;">
			<uv-icon name="email" size="66rpx" color="black" label="消息" label-size="48rpx"
				label-color="black"></uv-icon>
		</view>
		<uv-gap height="25rpx" bgColor="white"></uv-gap>

		<view class="list">
			<scroll-view scroll-y="true" lower-threshold="10rpx" :style="{height: listHeight + 'px'}">
				<uv-list>
					<uv-list-item>
						<view v-if="friendApplyList.length !== 0" class="friend-apply">好友申请</view>
					</uv-list-item>
					<uv-list-item v-for="item in friendApplyList" :key="item.messageId">
						<uv-gap height="25rpx" bgColor="white"></uv-gap>
						<view>
							<view class="friend-list-item">
								<view class="refuse" @click="refuseFriendApply(item)">x</view>
								<view class="friend-list-item-left">
									<!-- 头像 -->
									<view class="avatar">
										<uv-avatar :src="item.fromPlayer?.avatar" size="45rpx"
											shape="circle"></uv-avatar>
										<view class="is-online" v-show="item.fromPlayer?.online === '0'"></view>
									</view>
									<!-- 昵称 -->
									<view class="nickname-container">
										<view class="nickname">
											<uv-text size="18rpx" :lines="1"
												:text="item.fromPlayer?.nickName"></uv-text>
										</view>

										<view class="lv">
											<view class="lv-name">LV</view>
											<view class="lv-number">{{ item.fromPlayer?.level }}</view>
										</view>
									</view>
									<view class="game">
										<uv-image :src="item.fromPlayer?.img" height="40rpx" width="70rpx"
											mode="aspectFit"></uv-image>
									</view>
								</view>
								<view class="friend-list-item-right" @click="agreeFriendApply(item)">
									<uv-image
										src="https://img.js.design/assets/img/65633610d012312568672c4d.png#2af843932d0b958f0b7f05b752f7de58"
										height="40rpx" width="70rpx" mode="aspectFit"></uv-image>
								</view>
							</view>
						</view>
					</uv-list-item>
				</uv-list>
				<view v-if="friendApplyList.length !== 0">
					<uv-gap height="25rpx" bgColor="white"></uv-gap>
				</view>
				<uv-list>
					<uv-list-item>
						<view v-if="siteMsgList.length !== 0" class="message-title">站内信</view>
					</uv-list-item>
					<uv-list-item v-for="item in siteMsgList" :key="item.messageId">
						<uv-gap height="25rpx" bgColor="white"></uv-gap>
						<view>
							<view class="message-list-item">
								<view v-show="!item.isClick" class="close-message" @click="handleDeleteMessage(item)">×
								</view>
								<view class="message-list-item-title" @click="showMessageContent(item)">
									<uv-text size="32rpx" :lines="1" :text="item.message"></uv-text>
								</view>
								<view v-show="item.isClick">
									<view class="message-list-item-content">
										<view class="message-content">
											<uv-text size="28rpx" :text="item.message"></uv-text>
										</view>
										<view class="message-sender">
											<uv-text align="right" size="21rpx" :lines="1"
												:text="`${item.fromUid == 'SYSTEM' ? '系统消息' : `发送人：${item.fromPlayer?.nickName}`}`"></uv-text>
										</view>
										<view class="message-arrow" @click="hideMessageContent(item)">
											<uv-image
												src="https://img.js.design/assets/img/6561e48ed0d412d9d9f3cf48.png#58cc9da64782d7658e8d0a40bc9f9015"
												height="19rpx" width="33rpx" mode="aspectFit"></uv-image>
										</view>
									</view>
								</view>
							</view>
						</view>
					</uv-list-item>
				</uv-list>
				<uv-gap height="25rpx" bgColor="white"></uv-gap>
			</scroll-view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import {
		getCurrentInstance,
		ref,
		reactive,
		onMounted,
		onUnmounted,
		inject,
	} from 'vue';
	import { getMessageListAPI, setReadMessageAPI, operateMessageAPI } from '@/services/message';
	import { FriendApplyItem, SiteMsg } from '@/types/message.d.ts';
	import { agreeOrRefuseFriendApi } from '@/services/friend';
	import { useMessageStore } from '@/stores/modules/message'
	const {
		ctx
	} : any = getCurrentInstance();

	let listHeight = ref(0);
	onMounted(async () => {
		const dada = await ctx.$uv.getRect('.list');
		listHeight.value = dada.height;
		getMessageList();
		// setReadMessage();
		addListener()
	})
	onUnmounted(async () => {
		removeListener()
	})

	// 注册监听事件
	const addListener = () => {
		uni.$on('updateMessageList', onUpdateMessageList)
	}
	// 移除监听事件
	const removeListener = () => {
		uni.$off('updateMessageList', onUpdateMessageList);
	}
	// 刷新邀请列表
	const onUpdateMessageList = () => {
		getMessageList();
		// setReadMessage();
	}

	// 站内信列表
	const siteMsgList : SiteMsg[] = reactive([]);
	// 好友申请列表
	const friendApplyList : FriendApplyItem[] = reactive([]);

	const getMessageList = async () => {
		uni.showLoading({
			title: ''
		})
		getMessageListAPI({}).then((result) => {
			uni.hideLoading();
			siteMsgList.length = 0;
			friendApplyList.length = 0;
			result.data.siteMessage.forEach((item) => {
				item.isClick = false;
				siteMsgList.push(item);
			})
			friendApplyList.push(...result.data.friendApply);
			setReadMessage()
		})
	}

	const setReadMessage = async () => {
		setReadMessageAPI([]).then(() => {
			useMessageStore().setUnreadMessageNum(0);
		})
	}

	const showMessageContent = (item : SiteMsg) => {
		item.isClick = true;
	}
	const hideMessageContent = (item : SiteMsg) => {
		item.isClick = false;
	}
	const handleDeleteMessage = (item : SiteMsg) => {
		uni.showLoading({
			title: ''
		})
		operateMessageAPI([item.messageId]).then(() => {
			const index = siteMsgList.findIndex((player) => {
				return player.messageId == item.messageId;
			})
			if (index !== -1) {
				siteMsgList.splice(index, 1);
			}
			uni.hideLoading();
		})
	}

	// 从祖父组件注入方法
	const handleFriendRequest = inject<(item : FriendApplyItem, type : string, callback : (item : FriendApplyItem, type : string) => void) => void>('handleFriendRequest');

	const agreeFriendApply = async (item : FriendApplyItem) => {
		if (handleFriendRequest) {
			handleFriendRequest(item, "1", operationFriend);
		}
		// uni.showModal({
		// 	title: '提示',
		// 	content: '是否同意好友申请？',
		// 	success: async (res) => {
		// 		if (res.confirm) {
		// 			operationFriend(item, "1")
		// 		}
		// 	}
		// })
	}
	const refuseFriendApply = async (item : FriendApplyItem) => {
		if (handleFriendRequest) {
			handleFriendRequest(item, "2", operationFriend);
		}
		// uni.showModal({
		// 	title: '提示',
		// 	content: '是否拒绝好友申请？',
		// 	success: async (res) => {
		// 		if (res.confirm) {
		// 			operationFriend(item, "2")
		// 		}
		// 	}
		// })
	}
	const operationFriend = async (item : FriendApplyItem, operate : string) => {
		uni.showLoading({
			title: ''
		})
		// friendUid : string, messageId : string, operate : string
		agreeOrRefuseFriendApi(item.fromUid, item.messageId, operate).then(() => {
			uni.hideLoading();
			uni.showToast({
				title: operate === "1" ? '添加好友成功' : '已拒绝好友',
				icon: 'none'
			})

			// 同意或者拒绝后,从列表中栅除
			const index = friendApplyList.findIndex((player) => {
				return player.fromUid === item.fromUid;
			})
			if (index !== -1) {
				friendApplyList.splice(index, 1);
			}
		})
	}
</script>

<style scoped lang="scss">
	$margin: 25rpx;

	.container {
		display: flex;
		flex-direction: column;
		height: 100%;

		.list {
			flex: 1;
			height: 0;

			.friend-apply {
				background-color: $color-D43030;
				width: 201rpx;
				height: 73rpx;
				font-size: 28rpx;
				color: white;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 0rpx 21rpx 37rpx 0rpx;
			}

			.friend-list-item {

				background-color: $color-383838;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
				margin: 0 $margin;
				border-radius: 45rpx 28rpx 54rpx 10rpx;
				position: relative;

				.refuse {
					position: absolute;
					left: 7rpx;
					bottom: 18rpx;
					font-size: 22rpx;
					width: 12rpx;
					height: 12rpx;
					color: white;
				}

				.friend-list-item-left {
					flex: 1;
					margin: 10rpx 0rpx 10rpx 5rpx;
					background-color: white;
					display: flex;
					align-items: center;
					border-radius: 39rpx;

					.avatar {
						// 相对定位
						position: relative;
						margin: 15rpx;

						.is-online {
							width: 16rpx;
							height: 16rpx;
							border-radius: 50%;
							right: 0;
							bottom: 0;
							background-color: $color-B6FF1A;
							position: absolute;
						}
					}

					.nickname-container {
						flex: 2;
						display: flex;
						justify-content: space-between;
						align-items: center;
						background-color: $color-EFEFEF;
						border-radius: 8rpx;
						height: 45rpx;
						padding: 0 10rpx 0 18rpx;

						.nickname {
							font-family: AlimamaShuHeiTi;
						}

						.lv {
							display: flex;
							background-color: $color-F7E439;
							border-radius: 8rpx 11rpx 14rpx 11rpx;

							.lv-name {
								font-weight: bold;
								background-color: $color-383838;
								color: white;
								padding: 0rpx 10rpx;
								font-size: 16rpx;
								border-radius: 8rpx 11rpx 14rpx 11rpx;
							}

							.lv-number {
								margin-left: 4rpx;
								margin-right: 10rpx;
								font-weight: bold;
								font-size: 16rpx;
							}
						}
					}

					.game {
						padding-left: 25rpx;
						padding-right: 25rpx;
					}

					.mode {
						text-align: center;
						font-size: 18rpx;
						flex: 1;
					}
				}

				.friend-list-item-right {
					margin: 0 35rpx 0 25rpx;
					font-size: 28rpx;
					font-weight: bold;
					color: white;
				}

			}

			.message-title {
				background-color: $color-2A82E4;
				width: 201rpx;
				height: 73rpx;
				font-size: 28rpx;
				color: white;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 0rpx 21rpx 37rpx 0rpx;
				font-family: DeYiHei;
			}

			.message-list-item {
				background-color: $color-383838;
				display: flex;
				flex-direction: column;
				margin: 0 $margin;
				border-radius: 45rpx 28rpx 54rpx 14rpx;
				font-family: DeYiHei;
				position: relative;

				.close-message {
					position: absolute;
					font-size: 21rpx;
					line-height: 21rpx;
					font-family: AlimanmaShuHeiTi;
					color: white;
					bottom: 3rpx;
					left: 5rpx;
				}

				.message-list-item-title {
					// width: 100%;
					margin: 10rpx 15rpx 10rpx 5rpx;
					padding: 20rpx 38rpx;
					background-color: white;
					display: flex;
					align-items: center;
					border-radius: 45rpx 28rpx 54rpx 39rpx;
				}

				.message-list-item-content {
					display: flex;
					flex-direction: column;
					background-color: white;
					border-radius: 33rpx 28rpx 131rpx 21rpx;
					margin: 0rpx 15rpx 31rpx 5rpx;
					position: relative;

					.message-content {
						margin: 33rpx;
					}

					.message-sender {
						margin: 0 59rpx 28rpx 33rpx;
					}

					.message-arrow {
						position: absolute;
						bottom: 0;
						right: 0;
					}
				}
			}
		}
	}
</style>