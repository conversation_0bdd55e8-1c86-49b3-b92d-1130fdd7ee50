<template>
	<view class="container">
		<view style="margin-left: 59rpx; margin-top: 44rpx; font-family: AlimamaShuHeiTi;">
			<uv-icon name="friends" custom-prefix="custom-icon" size="66rpx" color="black" label="好友" label-size="48rpx"
				label-color="black"></uv-icon>
		</view>
		<uv-gap height="25rpx" bgColor="white"></uv-gap>

		<view class="search-container">
			<view class="filter">筛选游戏&赛制</view>
			<view class="search">
				<uv-input placeholder="搜索好友列表（UID）" placeholderStyle="color: rgba(0, 0, 0, 0.5);" fontSize="21rpx"
					:customStyle="searchStyle" @blur="onSearchBlur"></uv-input>

			</view>
		</view>


		<view class="list">
			<scroll-view scroll-y="true" lower-threshold="10rpx" :style="{height: listHeight + 'px'}"
				@scrolltolower="scrolltolower">
				<uv-list>
					<uv-list-item v-for="item in listData.friendList" :key="item.detail.uid">
						<uv-gap height="25rpx" bgColor="white"></uv-gap>
						<view v-if="!item.isClick">
							<view class="friend-list-item">
								<view class="friend-list-item-left" @click="onClickItemDown(item)">
									<!-- 头像 -->
									<view class="avatar">
										<uv-avatar :src="item.detail?.avatar" size="45rpx" shape="circle"></uv-avatar>
										<view class="is-online" v-show="item.detail?.online === '0'"></view>
									</view>
									<!-- 昵称 -->
									<view class="nickname-container">
										<view class="nickname">
											<uv-text size="18rpx" :lines="1" :text="item.detail?.nickName"></uv-text>
										</view>

										<view class="lv">
											<view class="lv-name">LV</view>
											<view class="lv-number">{{ item.detail?.level }}</view>
										</view>
									</view>
									<view class="game">
										<uv-image :src="item.detail?.img" height="40rpx" width="70rpx"
											mode="aspectFit"></uv-image>
									</view>
									<view class="mode">
										{{item.detail?.gameTypeName}}
									</view>
								</view>
								<!-- <view class="friend-list-item-right" @click="handleInviteFriend(item)">
									DUEL!
								</view> -->
								<image class="friend-list-item-right" src="@/static/images/icon_duel_start.png"
									mode="widthFix" @click="handleInviteFriend(item) "></image>
							</view>
						</view>
						<view v-else>
							<view class="friend-list-item-up">
								<view class="friend-list-item-up-top">
									<!-- 头像 -->
									<view class="avatar">
										<uv-avatar :src="item.detail?.avatar" size="45rpx" shape="circle"></uv-avatar>
										<view class="is-online" v-show="item.detail?.online === '0'"></view>
									</view>
									<view class="avatar-right-container">
										<view class="avatar-right-top">
											<view class="nickname-container">
												<view class="nickname">
													<uv-text size="18rpx" :lines="1"
														:text="item.detail?.nickName"></uv-text>
												</view>
												<view class="lv">
													<view class="lv-name">LV</view>
													<view class="lv-number">{{ item.detail?.level }}</view>
												</view>
											</view>
											<view class="game">
												<uv-image :src="item.detail?.img" height="40rpx" width="70rpx"
													mode="aspectFit"></uv-image>
											</view>

											<view class="mode">
												{{item.detail?.gameTypeName}}
											</view>

										</view>
										<view class="avatar-right-bottom">
											<uv-text :lines="2" :text="item.detail?.personalSignature" size="18rpx"
												lineHeight="25rpx"></uv-text>
										</view>

									</view>
								</view>
								<view class="friend-list-item-up-bottom">
									<view class="left">
										<view class="report" @click="onMaskingFriendClick(item)">
											<uv-icon name="warning" size="40rpx" color="black"></uv-icon>
										</view>
										<view class="deleteFriend" @click="onDeleteFriend(item)">
											<uv-icon name="shanchuhaoyou" custom-prefix="custom-icon" size="40rpx"
												color="black"></uv-icon>
										</view>
									</view>
									<view class="middle">
										<view class="uid" @click="onCopyUID(item)">
											UID: {{item.detail?.uid}}
										</view>
										<view class="email" @click="onSendMessage(item)">
											<view>
												<uv-icon name="email" size="40rpx" color="black"></uv-icon>

											</view>
										</view>
									</view>
									<view class="right" @click="onClickItemUp(item)">
										<uv-image
											src="https://img.js.design/assets/img/6561e48ed0d412d9d9f3cf48.png#58cc9da64782d7658e8d0a40bc9f9015"
											height="14rpx" width="24rpx" mode="aspectFit"></uv-image>
									</view>
								</view>
							</view>
						</view>
					</uv-list-item>
				</uv-list>
				<uv-load-more :status="listData.status" @loadmore="loadMore" />
			</scroll-view>

		</view>


		<view>
			<uv-popup ref="senderMessagePopup" :safeAreaInsetBottom="false" mode="bottom" round="52rpx"
				:closeOnClickOverlay="false">
				<tcg-sender-message-popup v-if="isSenderMessagePopupShow"
					@closeBtnClick="senderMessagePopupCloseBtnClick"
					:sendFriendItem="sendFriendItem"></tcg-sender-message-popup>
			</uv-popup>
		</view>

	</view>
</template>

<script setup lang="ts">
	import { getFriendListAPI, deleteFriendAPI, maskingFriendAPI } from '@/services/friend';
	import type { FriendItem } from '@/types/friend.d.ts';
	import {
		getCurrentInstance,
		ref,
		reactive,
		onMounted,
		inject,
	} from 'vue';
	const {
		ctx
	} : any = getCurrentInstance();


	let listData = reactive({
		friendList: [] as FriendItem[],
		pageIndex: 1,
		status: 'loadmore',
		uid: ''
	});
	let listHeight = ref(0);
	onMounted(async () => {
		const dada = await ctx.$uv.getRect('.list');
		listHeight.value = dada.height;
		getFriendList(1, listData.uid);
	})

	const getFriendList = async (pageNo : number, uid ?: string) => {
		listData.pageIndex = pageNo;
		listData.status = 'loading';
		uni.showLoading({
			title: ''
		})
		try {
			const result = await getFriendListAPI({
				friendUid: uid,
				pageNum: pageNo,
			});
			uni.hideLoading();
			if (listData.pageIndex === 1) {
				// 先清空数据
				listData.friendList.length = 0;
				// listData.friendList.push(...result.data.list);
				// 循环result.data.list,把每一项添加到listData.friendList中,并且给isClick属性设置默认值
				result.rows.forEach((item) => {
					item.isClick = false;
					listData.friendList.push(item);
				})

			} else {
				result.rows.forEach((item) => {
					item.isClick = false;
					listData.friendList.push(item);
				})
			}
			if (listData.friendList.length == result.total) {
				listData.status = 'nomore';
			} else {
				listData.pageIndex += 1;
				listData.status = 'loadmore';
			}
		} catch (e) {
			listData.status = 'loadmore';
		}
	}

	const searchStyle = {
		borderRadius: "10rpx 14rpx 25rpx 14rpx",
		height: "51rpx",
		marginRight: "58rpx",
		backgroundColor: "white"
	}

	const onClickItemDown = (item : FriendItem) => {
		item.isClick = true;
	}
	const onClickItemUp = (item : FriendItem) => {
		item.isClick = false;
	}

	const scrolltolower = () => {
		getFriendList(listData.pageIndex, listData.uid);
	}
	const loadMore = () => {
		getFriendList(listData.pageIndex, listData.uid);
	}
	const onSearchBlur = (text : string) => {
		listData.uid = text
		if (ctx.$uv.test.empty(text)) {
			listData.uid = ''
		}
		getFriendList(1, listData.uid);
	}
	const onCopyUID = (item : FriendItem) => {
		uni.setClipboardData({
			data: item.detail.uid!,
		})
	}

	// 从祖父组件注入删除好友方法
	const handleDeleteFriend = inject<(item : FriendItem, callback : (item : FriendItem) => void) => void>('handleDeleteFriend');

	const onDeleteFriend = (item : FriendItem) => {
		if (handleDeleteFriend) {
			handleDeleteFriend(item, deleteFriend);
		}
	}

	const deleteFriend = async (item : FriendItem) => {
		uni.showLoading({
			title: ''
		})
		deleteFriendAPI(item.detail.uid!).then(() => {
			uni.hideLoading();
			uni.showToast({
				title: '删除好友成功',
				icon: 'none'
			})
			getFriendList(1, listData.uid);
		})
	}

	// 从祖父组件注入举报好友方法
	const handleMaskingFriend = inject<(item : FriendItem, callback : (item : FriendItem) => void) => void>('handleMaskingFriend');

	const onMaskingFriendClick = (item : FriendItem) => {
		if (handleMaskingFriend) {
			handleMaskingFriend(item, maskingFriend);
		}
	}

	const maskingFriend = async (item : FriendItem) => {
		console.log("拉黑好友", item.detail.nickName);
		uni.showLoading({
			title: ''
		})
		maskingFriendAPI(item.detail.uid!).then(() => {
			uni.hideLoading();
			uni.showToast({
				title: '拉黑好友成功',
				icon: 'none'
			})
			getFriendList(1, listData.uid);
		})
	}


	const emit = defineEmits(["onInviteFriend"]);
	const handleInviteFriend = (item : FriendItem) => {
		emit("onInviteFriend", item);
	}

	let sendFriendItem = ref<FriendItem>();
	const senderMessagePopup = ref();
	const isSenderMessagePopupShow = ref(false);
	const onSendMessage = (item : FriendItem) => {
		sendFriendItem.value = item;
		senderMessagePopup.value.open();
		isSenderMessagePopupShow.value = true;
	}
	const senderMessagePopupCloseBtnClick = () => {
		senderMessagePopup.value.close();
		// 300ms后再设置为false，否则会导致弹窗关闭时，弹窗内容会闪一下
		setTimeout(() => {
			isSenderMessagePopupShow.value = false;
			sendFriendItem.value = undefined;
		}, 300);
	}
</script>

<style scoped lang="scss">
	$margin: 25rpx;

	.container {
		display: flex;
		flex-direction: column;
		height: 100%;

		.search-container {
			display: flex;
			flex-direction: row;
			align-items: center;
			background-color: $color-383838;
			font-family: DeYiHei;

			.filter {
				background-color: white;
				padding: 10rpx 16rpx;
				font-size: 21rpx;
				color: rgba(0, 0, 0, 0.5);
				border-radius: 10rpx 14rpx 25rpx 14rpx;
				margin: 10rpx $margin;
			}

			.search {
				flex: 1;
			}
		}

		.list {
			flex: 1;
			height: 0;

			.friend-list-item {
				background-color: $color-383838;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
				margin: 0 $margin;
				border-radius: 45rpx 28rpx 54rpx 21rpx;

				.friend-list-item-left {
					flex: 1;
					margin: 10rpx 0rpx 10rpx 5rpx;
					background-color: white;
					display: flex;
					align-items: center;
					border-radius: 45rpx 28rpx 54rpx 21rpx;

					.avatar {
						// 相对定位
						position: relative;
						margin: 15rpx;

						.is-online {
							width: 16rpx;
							height: 16rpx;
							border-radius: 50%;
							right: 0;
							bottom: 0;
							background-color: $color-B6FF1A;
							position: absolute;
						}
					}

					.nickname-container {
						flex: 2;
						display: flex;
						justify-content: space-between;
						align-items: center;
						background-color: $color-EFEFEF;
						border-radius: 8rpx;
						height: 45rpx;
						padding: 0 10rpx 0 18rpx;

						.nickname {
							font-family: AlimamaShuHeiTi;
						}

						.lv {
							display: flex;
							background-color: $color-F7E439;
							border-radius: 8rpx 11rpx 14rpx 11rpx;

							.lv-name {
								font-weight: bold;
								background-color: $color-383838;
								color: white;
								padding: 0rpx 10rpx;
								font-size: 16rpx;
								border-radius: 8rpx 11rpx 14rpx 11rpx;
							}

							.lv-number {
								margin-left: 4rpx;
								margin-right: 10rpx;
								font-weight: bold;
								font-size: 16rpx;
							}
						}
					}

					.game {
						padding-left: 25rpx;
						padding-right: 25rpx;
					}

					.mode {
						text-align: center;
						font-size: 18rpx;
						margin-right: 25rpx;
						font-family: DeYiHei;
					}
				}

				.friend-list-item-right {
					margin: 0 35rpx 0 25rpx;
					width: 100rpx;
					// font-size: 28rpx;
					// // font-weight: bold;
					// color: white;
					// font-family: AlimamaShuHeiTi;
				}
			}

			.friend-list-item-up {
				background-color: $color-383838;
				display: flex;
				flex-direction: column;
				margin: 0 $margin;
				border-radius: 45rpx 28rpx 54rpx 21rpx;

				.friend-list-item-up-top {
					margin: 10rpx 25rpx 10rpx 5rpx;
					background-color: white;
					display: flex;
					border-radius: 45rpx 28rpx 54rpx 21rpx;

					.avatar {
						// 相对定位
						position: relative;
						margin: 15rpx 15rpx 0 15rpx;
						height: 45rpx;

						.is-online {
							width: 16rpx;
							height: 16rpx;
							border-radius: 50%;
							right: 0;
							bottom: 0;
							background-color: $color-B6FF1A;
							position: absolute;
						}
					}

					.avatar-right-container {
						display: flex;
						width: 100%;
						flex-direction: column;
						margin: 15rpx 40rpx 15rpx 0rpx;

						.avatar-right-top {
							display: flex;

							.nickname-container {
								flex: 2;
								display: flex;
								justify-content: space-between;
								align-items: center;
								background-color: $color-EFEFEF;
								border-radius: 8rpx;
								height: 45rpx;
								padding: 0 10rpx 0 18rpx;

								.nickname {
									font-family: AlimamaShuHeiTi;
								}

								.lv {
									display: flex;
									background-color: $color-F7E439;
									border-radius: 8rpx 11rpx 14rpx 11rpx;

									.lv-name {
										font-weight: bold;
										background-color: $color-383838;
										color: white;
										padding: 0rpx 10rpx;
										font-size: 16rpx;
										border-radius: 8rpx 11rpx 14rpx 11rpx;
									}

									.lv-number {
										margin-left: 4rpx;
										margin-right: 10rpx;
										font-weight: bold;
										font-size: 16rpx;
									}
								}
							}

							.game {
								padding-left: 25rpx;
								padding-right: 25rpx;
							}

							.mode {
								font-size: 18rpx;
								align-self: center;
								margin-right: 25rpx;
								font-family: DeYiHei;
							}
						}

						.avatar-right-bottom {
							overflow: hidden;
							margin-top: 5rpx;
							padding: 10rpx;
							height: 70rpx;
							width: 100%;
							background-color: $color-FEF1C3;
							border-radius: 10rpx;
							font-family: DeYiHei;
						}
					}


				}

				.friend-list-item-up-bottom {
					display: flex;
					flex-direction: row;

					margin: 0 25rpx 12rpx 5rpx;

					.left {
						flex: 1;
						background-color: white;
						display: flex;
						justify-content: space-evenly;
						align-items: center;
						border-radius: 14rpx 21rpx 14rpx 23rpx;
					}

					.middle {
						flex: 3;
						background-color: white;
						border-radius: 10rpx 10rpx 28rpx 21rpx;
						display: flex;
						align-items: center;
						margin-left: 10rpx;

						.uid {
							margin: 15rpx 0rpx 15rpx 55rpx;
							padding: 5rpx 33rpx;
							background-color: $color-EFEFEF;
							border-radius: 8rpx;
							font-size: 18rpx;
							flex: 1;
							// 文字居中
							text-align: center;
							font-family: YouSheBiaoTiHei;
						}

						.email {
							margin-left: 52rpx;
							margin-right: 66rpx;
							width: 31rpx;
							display: flex;
							justify-content: center;
						}

					}

					.right {
						display: flex;
						align-items: center;
						margin-left: 33rpx;
						margin-right: 50rpx;
					}
				}
			}
		}
	}
</style>