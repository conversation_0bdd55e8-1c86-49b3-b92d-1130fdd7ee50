<template>
	<view class="container">
		<uv-gap height="14rpx" bgColor="#383838"></uv-gap>
		<image class="close-btn" @click="handleCloseBtnClick" src="../../static/images/icon_popup_close.png" mode="">
		</image>
		<view class="white-container">
			<uv-gap height="17rpx"></uv-gap>
			<view class="title">对战邀请</view>
			<uv-gap height="17rpx"></uv-gap>

			<view class="list">
				<scroll-view scroll-y="true" lower-threshold="10rpx">
					<uv-list :customStyle="{backgroundColor: 'rgba(0, 0, 0, 0)'}">
						<uv-list-item :customStyle="{backgroundColor: 'rgba(0, 0, 0, 0)'}">
							<view class="friend-invite-title">好友邀请</view>
						</uv-list-item>
						<uv-list-item :customStyle="{backgroundColor: 'rgba(0, 0, 0, 0)'}"
							v-for="(item, index) in friendInviteRoomList" :key="index">
							<view class="friend-invite-container">
								<view class="avatar">
									<uv-avatar :src="item.ownerAvatar" size="42rpx" shape="circle"></uv-avatar>
								</view>
								<view class="nickname">
									{{ item.ownerNickName }}
								</view>
								<view class="game">
									{{ item.gameName }} {{ item.gameTypeName }}
								</view>

								<view class="reject" @click="handleDeleteFriendInviteClick(item)">
									×
								</view>
								<view class="agree" @click="handleOnDuelClick(item)">
									接受
								</view>
							</view>
							<uv-gap height="12rpx"></uv-gap>

						</uv-list-item>
					</uv-list>
					<uv-gap height="5rpx"></uv-gap>
					<uv-list :customStyle="{backgroundColor: 'rgba(0, 0, 0, 0)'}">
						<uv-list-item :customStyle="{backgroundColor: 'rgba(0, 0, 0, 0)'}">
							<view class="public-room-title-container">
								<view class="public-room-title">公开房间</view>
								<view class="game-image">
									<uv-image :src="useMemberStore().profile.img" height="31rpx" width="67rpx"
										mode="aspectFit"></uv-image>
								</view>
								<view class="refresh" @click="handleRefreshListClick">
									<view class="">
										<uv-icon name="reload" color="black" size="17rpx"></uv-icon>
									</view>
								</view>
							</view>
							<uv-gap height="17rpx"></uv-gap>
						</uv-list-item>

						<uv-list-item :customStyle="{backgroundColor: 'rgba(0, 0, 0, 0)'}"
							v-for="(item, index) in publicInviteRoomList" :key="index">
							<view class="public-room-container">
								<view class="public-room-item-left"
									:style="{backgroundColor: item.hallType === 1 ? '#AFDF4B' : '#FF8D1A'}">
									{{item.hallType === 1 ? "好友" : "大厅"}}

								</view>

								<view class="public-room-item-middle">

									<view class="avatar">
										<uv-image :src="item.ownerAvatar" width="48rpx" height="48rpx"
											shape="circle"></uv-image>
									</view>

									<view class="nickname">
										{{ item.ownerNickName }}
									</view>

									<view class="mode">
										{{ item.gameTypeName }}
									</view>
								</view>
								<!-- <view class="public-room-item-right" @click="handleOnDuelClick(item)">
									Duel！
								</view> -->
								<image class="public-room-item-right" src="@/static/images/icon_duel_start.png"
									mode="widthFix" @click="handleOnDuelClick(item)"></image>
							</view>
							<uv-gap height="12rpx"></uv-gap>
						</uv-list-item>
					</uv-list>
				</scroll-view>
			</view>

		</view>
		<uv-gap height="37rpx" bgColor="#383838"></uv-gap>
	</view>
</template>

<script setup lang="ts">
	import { onMounted, onUnmounted, reactive } from 'vue';
	import { useMemberStore } from '@/stores/index';

	import { getInviteMessageAPI, joinRoomAPI } from '@/services/room';
	import { RoomListItem } from '@/types/room.d.ts';
	import { setReadMessageAPI } from '@/services/message';
	import route from '@/uni_modules/uv-ui-tools/libs/util/route.js';

	const emit = defineEmits(["closeBtnClick"]);
	function handleCloseBtnClick() {
		emit("closeBtnClick");
	}
	onMounted(() => {
		getInviteMessage(true);
		uni.$on('inviteRoomListUpdate', onInviteRoomListUpdate)
	})
	onUnmounted(() => {
		uni.$off('inviteRoomListUpdate', onInviteRoomListUpdate)
	})
	const onInviteRoomListUpdate = () => {
		getInviteMessage();
	}
	const handleRefreshListClick = () => {
		getInviteMessage(true);
	}
	const friendInviteRoomList : RoomListItem[] = reactive([])
	const publicInviteRoomList : RoomListItem[] = reactive([])

	const getInviteMessage = async (isShowLoading : boolean = false) => {
		if (isShowLoading) {
			uni.showLoading({
				title: ''
			});
		}
		getInviteMessageAPI().then((data) => {
			friendInviteRoomList.length = 0;
			publicInviteRoomList.length = 0;
			for (let i = 0; i < data.data.length; i++) {
				if (data.data[i].hallType == 1) {
					friendInviteRoomList.push(data.data[i]);
				} else {
					publicInviteRoomList.push(data.data[i]);
				}
			}
			if (isShowLoading) {
				uni.hideLoading();
			}
		})
	}

	const handleDeleteFriendInviteClick = (item : RoomListItem) => {
		setReadMessageAPI([item.messageId]).then(() => {
			// 把item从inviteRoomList中删除
			// const index = friendInviteRoomList.findIndex((inviteItem) => inviteItem.messageId == item.messageId);
			// if (index !== -1) {
			// 	friendInviteRoomList.splice(index, 1);
			// }
			getInviteMessage()
		})
	}

	const handleOnDuelClick = (item : RoomListItem) => {
		uni.showLoading({
			title: ''
		})
		const joinType = item.hallType == 1 ? 1 : 0;

		joinRoomAPI(item.rid!, joinType).then((res) => {
			uni.hideLoading()
			let roomDetail = encodeURIComponent(JSON.stringify(res.data));
			route({
				url: '/pagesRoom/room/room',
				params: {
					roomDetail: roomDetail
				}
			})
		}).finally(() => {
			getInviteMessage()
		})
	}
</script>

<style scoped lang="scss">
	.container {
		background-color: $color-383838;
		width: 694rpx;
		height: 80vh;
		// 元素使用相对定位
		position: relative;
		overflow: hidden;
		border-radius: 52rpx 19rpx 52rpx 49rpx;

		.close-btn {

			position: absolute;
			top: 16rpx;
			right: 16rpx;
			width: 36rpx;
			height: 33rpx;
		}

		.white-container {
			overflow: hidden;
			// 设置背景图片 平铺
			background-image: url("@/static/images/icon_fightInviteBtn_bg.png");
			background-repeat: repeat;
			height: calc(100% - 25rpx);
			width: 652rpx;
			margin: 0 26rpx 0 16rpx;
			border-radius: 52rpx 112rpx 52rpx 49rpx;
			display: flex;
			flex-direction: column;

			.title {
				font-size: 63rpx;
				line-height: 63rpx;
				font-family: YouSheBiaoTiHei;
				text-align: center;
			}

			.list {
				flex: 1;
				height: 0;

				.friend-invite-title {
					font-size: 35rpx;
					line-height: 45rpx;
					font-family: YouSheBiaoTiHei;
					margin-left: 24rpx;
				}

				.friend-invite-container {
					height: 78rpx;
					opacity: 1;
					background: linear-gradient(90deg, rgba(182, 255, 23, 0.45) 0%, rgba(128, 128, 128, 0.1) 100%);
					display: flex;
					align-items: center;

					.avatar {
						margin-left: 23rpx;
					}

					.nickname {
						flex: 1;
						width: 0;
						margin-left: 7rpx;
						background-color: rgba(204, 204, 204, 0.32);
						line-height: 38rpx;
						font-size: 18rpx;
						padding: 0 14rpx;
						font-family: AlimamaShuHeiTi;
						// 换行
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						// 换行
						border-radius: 7rpx;
					}

					.game {
						flex: 1.3;
						width: 0;
						margin-left: 7rpx;
						background-color: rgba(204, 204, 204, 0.32);
						line-height: 38rpx;
						font-size: 21rpx;
						padding: 0 14rpx;
						font-family: AlimamaShuHeiTi;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						border-radius: 7rpx;
					}

					.reject {
						margin-left: 12rpx;
						background-color: rgba(212, 48, 48, 0.89);
						line-height: 38rpx;
						font-size: 31rpx;
						width: 47rpx;
						font-family: YouSheBiaoTiHei;
						text-align: center;
						color: white;
						border-radius: 46%;
					}

					.agree {
						margin: 0 16rpx 0 14rpx;
						background-color: rgba(191, 245, 76, 0.89);
						line-height: 38rpx;
						font-size: 18rpx;
						width: 91rpx;
						font-family: AlimamaShuHeiTi;
						text-align: center;
						border-radius: 35rpx;
					}
				}

				.public-room-title-container {
					height: 45rpx;
					display: flex;
					align-items: center;

					.public-room-title {
						line-height: 45rpx;
						font-size: 35rpx;
						font-family: YouSheBiaoTiHei;
						margin-left: 24rpx;
					}

					.game-image {
						margin-left: 19rpx;
					}

					.refresh {
						width: 91rpx;
						height: 31rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						background-color: rgba(179, 242, 41, 0.88);
						border-radius: 24rpx;
						margin-right: 16rpx;
						margin-left: auto;
					}
				}

				.public-room-container {
					height: 85rpx;
					margin: 0 7rpx 0 3rpx;
					display: flex;
					align-items: center;
					background-color: rgba(56, 56, 56, 0.86);
					border-radius: 28rpx 21rpx 35rpx 28rpx;

					.public-room-item-left {
						border-radius: 21rpx 31rpx;
						line-height: 59rpx;
						width: 59rpx;
						font-size: 21rpx;
						font-family: YouSheBiaoTiHei;
						margin-left: 14rpx;
						text-align: center;
					}

					.public-room-item-middle {
						flex: 1;
						width: 0;
						height: 63rpx;
						background-color: white;
						margin-left: 9rpx;
						border-radius: 14rpx 14rpx 30rpx 14rpx;
						display: flex;
						align-items: center;

						.avatar {
							margin-left: 10rpx;
						}

						.nickname {
							flex: 2;
							width: 0;
							// 换行
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							// 换行
							line-height: 45rpx;
							padding: 0 7rpx;
							margin-left: 7rpx;
							font-size: 21rpx;
							font-family: AlimamaShuHeiTi;
							border-radius: 7rpx;
							background-color: rgba(238, 238, 238, 1);
						}

						.mode {
							flex: 1;
							width: 0;
							// 换行
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							// 换行
							line-height: 45rpx;
							padding: 0 7rpx;
							margin-left: 9rpx;
							margin-right: 26rpx;
							font-size: 21rpx;
							font-family: DeYiHei;
							border-radius: 7rpx;
							background-color: rgba(238, 238, 238, 1);
							text-align: center;
						}
					}

					.public-room-item-right {
						margin: 0 12rpx;

						width: 100rpx;
						// font-size: 35rpx;
						// color: black;
						// transform: rotate(-4.6deg);
						// -webkit-text-stroke: 2rpx white;
						// font-family: AlimamaShuHeiTi;
					}
				}
			}
		}
	}
</style>