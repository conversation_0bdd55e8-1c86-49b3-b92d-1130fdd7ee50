<template>
	<view class="contont">
		<view class="header" :style="{ marginTop : header_top + 'px'}">
			<!-- 用户信息卡片 -->
			<view class="user-card card">
				<!-- 用户信息卡片上部分 -->
				<view class="user-card-top">
					<!-- 用户信息卡片上左部分 -->
					<view class="user-card-top-left">
						<button class="avatar" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
							<uv-avatar :src="memberStore.profile.avatar" size="60rpx" shape="circle"></uv-avatar>
						</button>

						<view :style="lvBgColor" class="lv">
							<view class="lv-name">LV</view>
							<view class="lv-number">{{memberStore.profile.level}}</view>
						</view>
					</view>
					<!-- 用户信息卡片上右部分 -->
					<view class="user-card-top-right">
						<view class="username" @click="handleUserNameClick">
							<uv-text size="14rpx" :lines="1" :text="memberStore.profile.nickName"></uv-text>
						</view>

						<view class="info" @click="onPersonalSignatureEdit">
							<uv-text :custom-style="{height:'48rpx'}" line-height="16rpx" size="14rpx" :lines="3"
								:text="memberStore.profile.personalSignature"></uv-text>
							<view class="icon">
								<uv-icon name="edit-pen" color="#8E7C6D" size="28rpx"></uv-icon>
							</view>

						</view>

					</view>

				</view>
				<!-- 用户信息卡片下部分 -->
				<view class="user-card-bottom">
					<view class="uid" @click="onCopyUID">
						UID: {{memberStore.profile.uid}}
						<view v-if="isShowCopyUID" class="uid-copy">已复制</view>
					</view>
					<view class="scan" @click="handleOpenScanClick">
						<view>
							<uv-icon name="scan" color="#8E7C6D" size="28rpx"></uv-icon>
						</view>
					</view>
				</view>
			</view>
			<!-- 功能卡片 -->
			<view class="feature-card card">
				<!-- 功能卡片上部分 -->
				<view class="feature-card-top">
					<!-- 功能卡片下部分 -->
					<view :style="officialWebsiteFontColor" class="feature-card-top-left"
						@click="onCopyOfficialWebsite">
						{{ officialWebsite }}
					</view>
					<view class="feature-card-top-middle">
						<image src="@/static/images/icon_help.png" style="width: 50rpx;"
							:style="{height: featureCardTopMiddleImageHeight + 'px'}" mode="aspectFit"></image>
					</view>
					<view class="feature-card-top-right" @click="onHelpClick">帮助</view>
				</view>
				<view class="feature-card-bottom">
					<view class="feature-card-bottom-grid friends" @click="onFriendBtnClick">
						<uv-icon name="friends" custom-prefix="custom-icon" size="40rpx" color="black"></uv-icon>
					</view>
					<view class="feature-card-bottom-grid email" @click="onMessageBtnClick">
						<uv-icon name="email" size="40rpx" color="black">

						</uv-icon>
						<view class="badge">
							<uv-badge max="99" :value="messageStore.unreadMessageNum"></uv-badge>
						</view>

					</view>
					<view class="feature-card-bottom-grid search" @click="onSearchBtnClick">
						<uv-icon name="search" size="40rpx" color="black"></uv-icon>
					</view>
					<view class="feature-card-bottom-grid file-text" @click="onFeedbackClick">
						<uv-icon name="file-text" size="40rpx" color="black"></uv-icon>
					</view>
				</view>
			</view>
		</view>
	</view>
	<!-- 昵称编辑弹窗 -->
	<!-- <view>
		<uv-modal ref="nicknameModal" title="请输入昵称" :closeOnClickOverlay="false" showCancelButton
			@confirm="nicknameModalConfirm" @close="nicknameModalClose" :asyncClose="true">
			<view style="width: 100%">
				<uv-input v-model="nickname" placeholder="请输入昵称" border="surround" type="nickname"></uv-input>
			</view>
		</uv-modal>
	</view> -->
	<!-- 个性签名弹窗 -->
	<!-- <view>
		<uv-modal ref="personalSignatureModal" title="请输入个性签名" :closeOnClickOverlay="false" showCancelButton
			@confirm="personalSignatureModalConfirm" @close="personalSignatureModalClose" :asyncClose="true">
			<view style="width: 100%">
				<uv-textarea v-model="personalSignature" placeholder="请输入个性签名" maxlength="30"></uv-textarea>
			</view>
		</uv-modal>
	</view> -->
	<!-- 好友列表弹窗 -->
	<view>
		<uv-popup ref="friendListPopup" :safeAreaInsetBottom="false" mode="bottom" round="80rpx"
			:closeOnClickOverlay="false">
			<tcg-custom-popup v-if="isFriendListShow" @closeBtnClick="friendListPopupCloseBtnClick">
				<tcg-friend-list style="height: 100%" @onInviteFriend="onInviteFriend"></tcg-friend-list>
			</tcg-custom-popup>
		</uv-popup>
	</view>

	<!-- 消息列表弹窗 -->
	<view>
		<uv-popup ref="messageListPopup" :safeAreaInsetBottom="false" mode="bottom" round="80rpx"
			:closeOnClickOverlay="false">
			<tcg-custom-popup v-if="isMessageListShow" @closeBtnClick="messageListPopupCloseBtnClick">
				<tcg-message-list style="height: 100%"></tcg-message-list>
			</tcg-custom-popup>
		</uv-popup>
	</view>

	<!-- 搜索列表弹窗 -->
	<view>
		<uv-popup ref="searchListPopup" :safeAreaInsetBottom="false" mode="bottom" round="80rpx"
			:closeOnClickOverlay="false">
			<tcg-custom-popup v-if="isSearchListShow" @closeBtnClick="searchListPopupCloseBtnClick">
				<tcg-search-list style="height: 100%"></tcg-search-list>
			</tcg-custom-popup>
		</uv-popup>
	</view>

	<!-- 反馈弹窗 -->
	<view>
		<uv-popup ref="feedbackPopup" :safeAreaInsetBottom="false" mode="bottom" round="80rpx"
			:closeOnClickOverlay="false">
			<tcg-custom-popup v-if="isFeedbackShow" @closeBtnClick="feedbackPopupCloseBtnClick">
				<tcg-feedback style="height: 100%" @onCloseFeedbackPopup="feedbackPopupCloseBtnClick"></tcg-feedback>
			</tcg-custom-popup>
		</uv-popup>
	</view>
</template>

<script setup lang="ts">
	import throttle from '@/uni_modules/uv-ui-tools/libs/function/throttle.js';
	import { updateMemberProfileAPI, uploadMemberAvatarAPI } from '@/services/profile';
	import {
		onReady
	} from '@dcloudio/uni-app';
	import {
		computed,
		getCurrentInstance,
		ref,
	} from 'vue';
	import { useMemberStore } from '@/stores/index'
	import { useMessageStore } from '@/stores/modules/message'
	import { FriendItem } from '@/types/friend.d.ts';
	import { scanQRCodeAPI } from '@/services/login';

	let featureCardTopMiddleImageHeight = ref(0);
	const {
		ctx
	} : any = getCurrentInstance();
	onReady(async () => {
		ctx.$uv.getRect('.feature-card-top-middle').then(res => {
			featureCardTopMiddleImageHeight.value = res.height;
		})
	})
	// header起始位置
	const header_top = uni.getMenuButtonBoundingClientRect().top + uni.getMenuButtonBoundingClientRect().height + 5
	const memberStore = useMemberStore()
	const messageStore = useMessageStore()

	const onChooseAvatar = async (ev : any) => {

		const avatar = ev.detail.avatarUrl as string;
		uploadMemberAvatarAPI({ name: 'avatar', filePath: avatar }).then(res => {
			memberStore.profile.avatar = res.data.imgUrl
			uni.showToast({
				icon: "success",
				title: "修改头像成功"
			})
		})
	}
	// 计算属性设置class为lv的背景色
	const lvBgColor = computed(() => {
		const lv = memberStore.profile.level;
		if (lv == 1) {
			return 'background-color: #F7E439'
		} else if (lv == 2) {
			return 'background-color: #FFC300'
		} else if (lv == 3) {
			return 'background-color: #FF8D1A'
		} else if (lv == 4) {
			return 'background-color: #43CF7C'
		} else if (lv == 5) {
			return 'background-color: #FF5733'
		} else {
			return 'background-color: #E33C64'
		}
	});


	const handleOpenScanClick = () => {
		uni.scanCode({
			success: (res) => {
				console.log(res.result);
				// 对res.result按照/进行分割字符串,并取出数组最后一项
				const uuid = res.result.split('/').pop();
				scanQRCode(uuid)
			},
		});
	}
	const scanQRCode = (uuid : string) => {
		uni.showLoading({
			title: ''
		})
		scanQRCodeAPI({
			uuid: uuid
		}).then(res => {
			uni.hideLoading()
			uni.showToast({
				icon: "none",
				title: "扫码成功"
			})
		})
	}

	// const nicknameModal = ref();
	const handleUserNameClick = async () => {
		throttle(() => {
			uni.navigateTo({
				url: '/pagesIndex/pages/edit-user-nickname/edit-user-nickname'
			})
		}, 1000)
		// nicknameModal.value.open();
	}
	// let nickname = ref(memberStore.profile.nickName);
	// const nicknameModalConfirm = async () => {
	// 	if (ctx.$uv.test.empty(nickname.value)) {
	// 		nicknameModal.value.close();
	// 		return
	// 	}

	// 	try {
	// 		await updateMemberProfileAPI({ nickName: nickname.value });
	// 		memberStore.profile.nickName = nickname.value;
	// 		uni.showToast({
	// 			icon: "success",
	// 			title: "修改昵称成功"
	// 		})
	// 	} finally {
	// 		nicknameModal.value.close();
	// 	}
	// }
	// const nicknameModalClose = () => {
	// 	nickname.value = memberStore.profile.nickName;
	// }
	const isShowCopyUID = ref(false);
	const onCopyUID = async () => {
		uni.setClipboardData({
			data: memberStore.profile.uid!,
			complete: () => {
				isShowCopyUID.value = true;
				setTimeout(() => {
					isShowCopyUID.value = false;
				}, 2000);
			}
		})
	}
	const officialWebsite = ref('复制官网');
	const officialWebsiteFontColor = ref('');
	const onCopyOfficialWebsite = async () => {
		uni.setClipboardData({
			data: 'https://www.duelchannel.com',
			complete: () => {
				officialWebsite.value = '已复制';
				officialWebsiteFontColor.value = 'color: red';
				setTimeout(() => {
					officialWebsite.value = '复制官网';
					officialWebsiteFontColor.value = '';
				}, 2000);
			}
		})
	}
	// const personalSignatureModal = ref();
	const onPersonalSignatureEdit = () => {
		// personalSignatureModal.value.open();
		throttle(() => {
			uni.navigateTo({
				url: '/pagesIndex/pages/edit-user-signature/edit-user-signature'
			})
		}, 1000)
	}

	// let personalSignature = ref(memberStore.profile.personalSignature);
	// const personalSignatureModalConfirm = async () => {
	// 	try {
	// 		await updateMemberProfileAPI({ personalSignature: personalSignature.value });
	// 		memberStore.profile.personalSignature = personalSignature.value;
	// 		uni.showToast({
	// 			icon: "success",
	// 			title: "修改个性签名成功"
	// 		})
	// 	} finally {
	// 		personalSignatureModal.value.close();
	// 	}
	// }
	// const personalSignatureModalClose = () => {
	// 	personalSignature.value = memberStore.profile.personalSignature;
	// }

	const onHelpClick = () => {
		uni.navigateTo({
			url: '/pagesHelp/pages/help/help'
		})
	}

	const friendListPopup = ref();
	const isFriendListShow = ref(false);
	const onFriendBtnClick = () => {
		friendListPopup.value.open();
		isFriendListShow.value = true;
	}
	const friendListPopupCloseBtnClick = () => {
		friendListPopup.value.close();
		// 300ms后再设置为false，否则会导致弹窗关闭时，弹窗内容会闪一下
		setTimeout(() => {
			isFriendListShow.value = false;
		}, 300);
	}
	const emit = defineEmits(["onHomeInviteFriend", "onRoomInviteFriend"]);

	const onInviteFriend = (item : FriendItem) => {
		friendListPopupCloseBtnClick()
		let routes = getCurrentPages(); // 获取当前打开过的页面路由数组
		let curRoute = routes[routes.length - 1].route
		if (curRoute === "pages/index/index") {
			emit("onHomeInviteFriend", item);
		} else if (curRoute === "pagesRoom/room/room") {
			emit("onRoomInviteFriend", item);
		}
	}

	const messageListPopup = ref();
	const isMessageListShow = ref(false);
	const onMessageBtnClick = () => {
		messageListPopup.value.open();
		isMessageListShow.value = true;
	}
	const messageListPopupCloseBtnClick = () => {
		messageListPopup.value.close();
		// 300ms后再设置为false，否则会导致弹窗关闭时，弹窗内容会闪一下
		setTimeout(() => {
			isMessageListShow.value = false;
		}, 300);
	}

	const searchListPopup = ref();
	const isSearchListShow = ref(false);
	const onSearchBtnClick = () => {
		searchListPopup.value.open();
		isSearchListShow.value = true;
	}
	const searchListPopupCloseBtnClick = () => {
		searchListPopup.value.close();
		// 300ms后再设置为false，否则会导致弹窗关闭时，弹窗内容会闪一下
		setTimeout(() => {
			isSearchListShow.value = false;
		}, 300);
	}


	const feedbackPopup = ref();
	const isFeedbackShow = ref(false);
	const onFeedbackClick = () => {
		feedbackPopup.value.open();
		isFeedbackShow.value = true;
	}
	const feedbackPopupCloseBtnClick = () => {
		feedbackPopup.value.close();
		// 300ms后再设置为false，否则会导致弹窗关闭时，弹窗内容会闪一下
		setTimeout(() => {
			isFeedbackShow.value = false;
		}, 300);
	}
</script>

<style scoped lang="scss">
	.contont {
		width: 100%;
		overflow: hidden;

		.header {
			margin-top: 20px;
			padding-left: 25rpx;
			padding-right: 25rpx;
			display: flex;

			.card {
				flex: 1;
				display: flex;
				flex-direction: column;
			}

			.user-card {
				background-color: $color-383838;
				margin-right: 12.5rpx;
				border-radius: 90rpx 40rpx 60rpx 40rpx;
				overflow: hidden;

				.user-card-top {
					flex: 1;
					display: flex;
					background-color: white;
					margin: 10rpx 20rpx 0rpx 10rpx;
					border-radius: 80rpx 35rpx 20rpx 20rpx;

					.user-card-top-left {
						display: flex;
						// 垂直布局
						flex-direction: column;
						margin-left: 30rpx;
						align-items: center;

						.avatar {
							margin-top: 20rpx;
							width: 60rpx;
							height: 60rpx;
							padding: 0;
							border-radius: 30rpx;
						}

						.lv {
							margin-top: 10rpx;
							display: flex;
							background-color: $color-F7E439;
							border-radius: 8rpx 11rpx 14rpx 11rpx;

							.lv-name {
								// font-family: PassionOne;
								font-weight: bold;
								background-color: $color-383838;
								color: white;
								padding: 0rpx 10rpx;
								font-size: 18rpx;
								line-height: 23rpx;
								border-radius: 8rpx 11rpx 14rpx 11rpx;
							}

							.lv-number {
								// font-family: BerkshireSwash;
								margin-left: 4rpx;
								margin-right: 10rpx;
								font-weight: bold;
								font-size: 18rpx;
								line-height: 23rpx;
							}
						}
					}

					.user-card-top-right {
						flex: 1;
						width: 0;
						display: flex;
						flex-direction: column;
						margin-left: 10rpx;
						margin-right: 15rpx;

						.username {
							font-family: AlimamaShuHeiTi;
							margin-top: 25rpx;
							margin-right: 30rpx;
							padding: 3rpx 10rpx;
							background-color: $color-EFEFEF;
							border-radius: 8rpx;
							font-size: 18rpx;
						}

						.info {
							font-family: DeYiHei;
							margin: 10rpx 0rpx 15rpx 0rpx;
							padding-top: 10rpx;
							padding-right: 40rpx;
							padding-bottom: 30rpx;
							padding-left: 10rpx;
							border-radius: 8rpx;
							background-color: $color-FEF1C3;
							position: relative;

							.icon {
								position: absolute;
								right: 10rpx;
								bottom: 10rpx;
							}
						}
					}


				}

				.user-card-bottom {
					background-color: white;
					margin: 10rpx 20rpx 20rpx 10rpx;
					border-radius: 20rpx 25rpx 50rpx 35rpx;
					display: flex;
					align-items: center;

					.uid {
						font-family: YouSheBiaoTiHei;
						margin: 15rpx 0rpx 15rpx 30rpx;
						padding: 5rpx 0rpx;
						background-color: $color-EFEFEF;
						border-radius: 8rpx;
						font-size: 18rpx;
						flex: 1;
						// 文字居中
						text-align: center;
						position: relative;

						.uid-copy {
							position: absolute;
							left: 0;
							right: 0;
							top: 0;
							bottom: 0;
							display: flex;
							border-radius: 8rpx;
							font-size: 21rpx;
							align-items: center;
							justify-content: center;
							background-color: rgba(204, 204, 204, 0.9);
						}
					}

					.scan {
						margin-left: 20rpx;
						margin-right: 20rpx;
						width: 70rpx;
						border-radius: 30rpx;
						background-color: $color-BAFA2C;
						display: flex;
						justify-content: center;
						padding: 5rpx 0rpx;
					}


				}
			}

			.feature-card {
				margin-left: 12.5rpx;
				display: flex;
				flex-direction: column;

				.feature-card-top {
					flex: 1;
					border-radius: 20rpx;
					background-color: $color-383838;
					display: flex;
					padding: 10rpx 20rpx 15rpx 10rpx;

					.feature-card-top-left {
						border-radius: 20rpx 0rpx 0rpx 20rpx;
						flex: 2;
						background-color: white;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 30rpx;
						font-family: AlimamaShuHeiTi;
					}

					.feature-card-top-middle {
						background-color: white;
						display: flex;
						align-items: center;

					}

					.feature-card-top-right {
						border-radius: 0rpx 20rpx 20rpx 0rpx;
						flex: 1;
						background-color: white;
						text-align: center;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 30rpx;
						font-family: DeYiHei;
					}
				}

				.feature-card-bottom {
					margin-top: 13rpx;
					border-radius: 20rpx;
					background-color: $color-383838;
					display: flex;
					justify-content: space-evenly;

					// padding: ;
					.feature-card-bottom-grid {
						height: 60rpx;
						width: 60rpx;
						margin: 15rpx 0;
						background-color: white;
						display: flex;
						justify-content: center;
						border-radius: 15rpx;
					}


					.email {
						position: relative;

						.badge {
							position: absolute;
							right: 0;
							top: 0;
							transform: translate(50%, -50%);
						}
					}
				}
			}
		}
	}
</style>