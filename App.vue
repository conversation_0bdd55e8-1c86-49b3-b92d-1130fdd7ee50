<script setup lang="ts">
	import {
		onLaunch,
		onShow,
		onHide,
	} from "@dcloudio/uni-app";

	import { useShareStore } from '@/stores/modules/share'
	import { empty } from '@/uni_modules/uv-ui-tools/libs/function/test.js';

	import { isLogin } from '@/utils/loginHelper'
	import { getMemberProfileByUidAPI } from '@/services/profile'
	import { useMemberStore } from '@/stores/index';
	// 控制台报 这个警告. 小程序端 style 暂不支持 p 标签选择器，推荐使用 class 选择器，详情参考：https://uniapp.dcloud.net.cn/tutorial/migration-to-vue3.html#style​
	// 是因为需要管理后台用到的editer编辑器需要样式, 所以在style文件夹 里面引用了quill文件夹这三个类,导致报上面的警告
	onLaunch((option) => {
		console.log('App Launch', option)
		if (!empty(option.query.roomId) && !empty(option.query.roomOwnerUid)) {
			useShareStore().setShareParams(option.query);
		}
	});

	onShow((option) => {
		console.log('App Show', option)
		uni.$emit('TcgAppOnShow')
		if (!empty(option.query.roomId) && !empty(option.query.roomOwnerUid)) {
			useShareStore().setShareParams(option.query);
		}
		if (isLogin()) {
			getMemberProfileByUid()
		}
		// 保持屏幕常亮
		uni.setKeepScreenOn({
			keepScreenOn: true
		});
		checkUpdateVersion();
	});
	onHide(() => {
		console.log('App Hide')
	});

	const getMemberProfileByUid = () => {
		getMemberProfileByUidAPI(useMemberStore().profile.uid).then(res => {
			useMemberStore().setProfile(res.data)
		})
	}
	const checkUpdateVersion = () => {
		const updateManager = uni.getUpdateManager()
		// 检查更新版本
		updateManager.onCheckForUpdate(function (res) {
			// 请求完新版本信息的回调
			if (res.hasUpdate) {
				// 检测到有新版本，静默下载新版本
				updateManager.onUpdateReady(function () {
					updateManager.applyUpdate()
				})
			}
		})
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	// 引入index样式
	@import '@/styles/index.scss';
</style>