<template>
	<view class="container">
		<uv-navbar placeholder="true">
			<template v-slot:left>
				<image src="/pagesHelp/static/images/back_left.png" style="width: 42rpx; height: 42rpx;"
					@click="onNavLeftClick"></image>
			</template>
			<template v-slot:center>
				<view style="display: flex; align-items: center; justify-content: center;">
					<image
						src="https://img.js.design/assets/img/6555dfe290ab84325b9e5532.png#5139c6bae75c94f15db8707e375758f0"
						style="width: 44rpx; height: 44rpx;">
					</image>
					<text style="font-size: 42rpx; font-family: AlimamaShuHeiTi;">帮助</text>
				</view>
			</template>
		</uv-navbar>
		<uv-gap height="30rpx"></uv-gap>
		<view class="top">
			<!-- <view class="top-content"
				@click="onPushToDetailNoSwiperPage('1768671833097375745', '平台使用方法', '/static/logo.png')">平台使用方法</view> -->
			<view class="top-content" @click="showInstructions">平台使用方法</view>
			<uv-line direction="col" length="76rpx" color="rgba(166, 166, 166, 1)"></uv-line>
			<view class="top-content"
				@click="onPushToDetailNoSwiperPage('1768671801744953346', '关于TCG', '/static/logo.png')">关于TCG</view>
		</view>
		<uv-gap height="40rpx"></uv-gap>
		<view class="list-content">
			<uv-list>
				<uv-list-item v-for="listItem in helpArray" :key="listItem.id">
					<view class="list-item">
						<view class="list-item-content">
							<image :src="listItem.iconUrl" class="game-logo"></image>
							<text class="game-text"
								@click="onClickToPush(listItem, listItem.iconUrl)">{{ listItem.title }}</text>
							<text class="right-arrow">></text>
							<view class="rules-content">
								<uv-gap height="15rpx"></uv-gap>
								<view class="rules-item">
									<view class="fengexian"></view>

									<view class="item-content"
										v-for="(ruleItem, index) in sliceArray(listItem.children)" :key="ruleItem.id"
										:class="{'hidden-border-bottom': hiddenBorderBottom(index, sliceArray(listItem.children).length)}"
										@click="onClickToPush(ruleItem, listItem.iconUrl)">
										<view class="item-title">{{ ruleItem.title }}</view>
										<view :class="{'hidden-badge': hiddenBadge(ruleItem)}" class="badge">新</view>
									</view>
								</view>
								<uv-gap height="15rpx"></uv-gap>
							</view>
						</view>
						<view style="margin: 0 90rpx;">
							<uv-gap height="1.74rpx" bgColor="rgba(229, 229, 229, 0.46)"></uv-gap>
						</view>
					</view>
				</uv-list-item>
			</uv-list>
		</view>
		<uv-gap height="40rpx"></uv-gap>
	</view>

	<!-- 是否显示平台使用说明 -->
	<tcg-instructions :isShowTcgInstructions="isShowTcgInstructions"
		@onInstructionsClick="onInstructionsClick"></tcg-instructions>
</template>

<script setup lang="ts">
	import {
		onLoad,
	} from '@dcloudio/uni-app';
	import { getHelpListAPI } from '@/services/help';
	import { HelpList } from '@/types/help.d.ts'
	import { ref } from 'vue';
	import { empty } from '@/uni_modules/uv-ui-tools/libs/function/test.js';

	onLoad(() => {
		getHelpList();
	});

	let helpArray = ref<HelpList[]>([])
	const getHelpList = () => {
		// 获取帮助列表
		uni.showLoading({
			title: "加载中"
		});
		getHelpListAPI("1764273524673540097").then((res) => {
			uni.hideLoading();
			helpArray.value.length = 0;
			helpArray.value.push(...res.data);
		})
	};

	// 取数组前6个元素
	const sliceArray = (list : HelpList[]) => {

		if (empty(list)) {
			return [];
		}
		return list.slice(0, 6);
	};


	const onNavLeftClick = () => {
		uni.navigateBack();
	};
	const hiddenBorderBottom = (index : number, length : number) => {
		// 判断length是奇数还是偶数, 如果是奇数, 则最后一个不显示分割线, 如果是偶数, 则最后两个都不显示分割线
		return length % 2 === 0 ? index === length - 1 || index === length - 2 : index === length - 1;
	};
	const hiddenBadge = (listItem : HelpList) => {
		return !listItem.tag.split(',').includes('1');
	};
	const onClickToPush = (listItem : HelpList, iconUrl : string) => {
		if (listItem.type == 3) {
			uni.navigateTo({
				url: `/pagesHelp/pages/help/help-detail?id=${listItem.id}&name=${listItem.title}&iconUrl=${iconUrl}`
			})
		} else {
			uni.navigateTo({
				url: `/pagesHelp/pages/help/help-game-list?id=${listItem.id}&name=${listItem.title}&iconUrl=${iconUrl}`
			})
		}
	};

	const onPushToDetailNoSwiperPage = (id : string, title : string, iconUrl : string) => {
		uni.navigateTo({
			url: `/pagesHelp/pages/help/help-detail-no-swiper?id=${id}&title=${title}&iconUrl=${iconUrl}`
		})
	};

	const showInstructions = () => {
		isShowTcgInstructions.value = true;
	};
	const isShowTcgInstructions = ref(false);
	const onInstructionsClick = () => {
		isShowTcgInstructions.value = false;
	};
</script>

<style>
	page {
		background-image: url("@/static/images/yellow_bg.png");
		background-repeat: repeat;
	}
</style>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		height: 100%;
		// 设置背景图片 平铺
		// background-image: url("@/static/images/yellow_bg.png");
		// background-repeat: repeat;

		.top {
			margin: 0 25rpx;
			background-color: white;
			height: 136rpx;
			border-radius: 35rpx;
			//  边框
			border: 1.74rpx solid rgba(166, 166, 166, 1);
			display: flex;
			align-items: center;

			.top-content {
				width: 0;
				flex: 1;
				text-align: center;
				// 字体加粗
				font-weight: bold;
				font-size: 35rpx;
			}
		}

		.list-content {
			margin: 0 25rpx;
			background-color: white;
			border-radius: 35rpx;
			overflow: hidden;

			.list-item {
				display: flex;
				flex-direction: column;

				.list-item-content {
					height: 0;
					flex: 1;
					display: flex;
					align-items: center;

					.game-logo {
						margin-left: 40rpx;
						width: 112rpx;
						height: 52rpx;
					}

					.game-text {
						margin-left: 10rpx;
						font-size: 18rpx;
						width: 120rpx;
						text-align: center;
						// 省略号
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}

					.right-arrow {
						margin-left: 10rpx;
						font-size: 18rpx;
						color: rgba(166, 166, 166, 1);
					}

					.rules-content {
						width: 0;
						flex: 1;
						display: flex;
						flex-direction: column;
						margin: 0 30rpx;

						.rules-item {
							display: grid;
							grid-template-columns: 1fr 1fr;
							// 网格列间距
							column-gap: 44rpx;
							position: relative;

							.item-content {
								height: 50rpx;
								width: 100%;
								overflow: hidden;
								// 底部边框
								border-bottom: 1.74rpx solid rgba(229, 229, 229, 1);
								display: flex;
								align-items: center;
								justify-content: center;

								.item-title {
									// max-width: 100%;
									// flex: 1;
									font-size: 21rpx;
									// 省略号
									overflow: hidden;
									white-space: nowrap;
									text-overflow: ellipsis;
									text-align: center;
								}

								.badge {
									flex: 0 0 auto;
									margin-left: 4rpx;
									margin-bottom: 10rpx;
									font-size: 10rpx;
									line-height: 14rpx;
									height: 14rpx;
									width: 14rpx;
									text-align: center;
									color: white;
									background-color: rgba(212, 48, 48, 1);
									border-radius: 2rpx;
									visibility: visible;
								}

								.hidden-badge {
									visibility: hidden;
								}
							}

							// 隐藏item分割线底部边框
							.hidden-border-bottom {
								border-bottom: none;
							}

							.fengexian {
								position: absolute;
								width: 1.74rpx;
								background-color: rgba(229, 229, 229, 0.35);
								left: 50%;
								transform: translate(-50%);
								top: 20rpx;
								bottom: 20rpx;
							}
						}
					}
				}
			}
		}
	}
</style>