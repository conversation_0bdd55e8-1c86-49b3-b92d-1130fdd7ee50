<template>
	<view class="container">
		<uv-navbar placeholder="true">
			<template v-slot:left>
				<image src="/pagesHelp/static/images/back_left.png" style="width: 42rpx; height: 42rpx;"
					@click="onNavLeftClick"></image>
			</template>
			<template v-slot:center>
				<view style="display: flex; align-items: center; justify-content: center;">
					<image
						src="https://img.js.design/assets/img/6555dfe290ab84325b9e5532.png#5139c6bae75c94f15db8707e375758f0"
						style="width: 44rpx; height: 44rpx;">
					</image>
					<text style="font-size: 42rpx; font-family: AlimamaShuHeiTi;">帮助</text>
				</view>
			</template>
		</uv-navbar>
		<view class="top">
			<image :src="vdata.data.parentData.iconUrl"
				style="width: 112rpx; height: 52rpx; margin: 30rpx 0 30rpx 47rpx;" mode="aspectFit"></image>
			<text style="margin-left: 30rpx; font-size: 28rpx;">{{ vdata.data.parentData.title }}</text>
		</view>
		<uv-gap height="10rpx"></uv-gap>

		<view class="rule-content">
			<uv-parse :content="vdata.data.helpDetail.content"></uv-parse>
		</view>
		<uv-gap height="10rpx"></uv-gap>
	</view>
</template>

<script setup lang="ts">
	import {
		onLoad,
	} from '@dcloudio/uni-app';
	import { getHelpDetailAPI } from '@/services/help';
	import { HelpDetail } from '@/types/help.d.ts'
	import { reactive } from 'vue';
	const vdata = reactive({
		data: {
			parentData: {
				id: '',
				title: '',
				iconUrl: ''
			},
			helpDetail: {} as HelpDetail,
		}
	})

	onLoad((option) => {
		vdata.data.parentData.id = option.id;
		vdata.data.parentData.title = option.title;
		vdata.data.parentData.iconUrl = option.iconUrl;
		getHelpDetail(option.id);
	});

	const getHelpDetail = (id : string) => {
		// 获取帮助列表
		uni.showLoading({
			title: "加载中"
		});
		getHelpDetailAPI(id).then((res) => {
			uni.hideLoading();
			vdata.data.helpDetail = res.data;
		})
	};
	const onNavLeftClick = () => {
		uni.navigateBack();
	};
</script>

<style>
	page {
		background-image: url("@/static/images/yellow_bg.png");
		background-repeat: repeat;
	}
</style>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		height: 100%;
		// 设置背景图片 平铺
		// background-image: url("@/static/images/yellow_bg.png");
		// background-repeat: repeat;

		.top {
			background-color: white;
			display: flex;
			align-items: center;
		}

		.rule-content {
			margin: 0 20rpx;
			padding: 65rpx 40rpx;
			border-radius: 21rpx;
			background-color: rgba(204, 204, 204, 0.42);
		}
	}
</style>