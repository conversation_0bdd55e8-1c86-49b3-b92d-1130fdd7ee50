<template>
	<view class="container">
		<uv-navbar placeholder="true">
			<template v-slot:left>
				<image src="/pagesHelp/static/images/back_left.png" style="width: 42rpx; height: 42rpx;"
					@click="onNavLeftClick"></image>
			</template>
			<template v-slot:center>
				<view style="display: flex; align-items: center; justify-content: center;">
					<image
						src="https://img.js.design/assets/img/6555dfe290ab84325b9e5532.png#5139c6bae75c94f15db8707e375758f0"
						style="width: 44rpx; height: 44rpx;">
					</image>
					<text style="font-size: 42rpx; font-family: AlimamaShuHeiTi;">帮助</text>
				</view>
			</template>
		</uv-navbar>
		<view class="top">
			<uv-gap height="40rpx"></uv-gap>
			<image :src="vdata.data.parentData.iconUrl" style="width: 198rpx; height: 92rpx;"></image>
			<uv-gap height="40rpx"></uv-gap>
		</view>
		<uv-gap height="30rpx"></uv-gap>

		<view class="list-content">
			<uv-gap height="32rpx"></uv-gap>
			<view class="game-title">关于{{vdata.data.parentData.name}}</view>
			<uv-gap height="32rpx"></uv-gap>
			<view class="game-title-bottom-line"></view>
			<view class="rules-content">
				<uv-gap height="15rpx"></uv-gap>
				<view class="rules-item">
					<view class="item-content" v-for="item in helpArray" :key="item.id" @click="onRulesItemClick(item)">
						<view class="item-title">{{item.title}}</view>
					</view>
				</view>
				<uv-gap height="100rpx"></uv-gap>
			</view>
		</view>
		<uv-gap height="40rpx"></uv-gap>
	</view>
</template>

<script setup lang="ts">
	import {
		onLoad,
	} from '@dcloudio/uni-app';
	import { getHelpListAPI } from '@/services/help';
	import { HelpList } from '@/types/help.d.ts'
	import { reactive, ref } from 'vue';
	const vdata = reactive({
		data: {
			parentData: {
				id: '',
				name: '',
				iconUrl: ''
			},
		}
	})

	onLoad((option) => {
		vdata.data.parentData.id = option.id;
		vdata.data.parentData.name = option.name;
		vdata.data.parentData.iconUrl = option.iconUrl;
		getHelpList(option.id);
	});

	let helpArray = ref<HelpList[]>([])
	const getHelpList = (id : string) => {
		// 获取帮助列表
		uni.showLoading({
			title: "加载中"
		});
		getHelpListAPI(id).then((res) => {
			uni.hideLoading();
			helpArray.value.length = 0;
			helpArray.value.push(...res.data)
		})
	};

	// 过滤子节点, 只保留内容
	const filterNode = (list : HelpList[]) => {

		return list.filter(function (user) {
			return user.type === 3;
		})
	};

	const onRulesItemClick = (item : HelpList) => {

		if (item.type == 3) {
			uni.navigateTo({
				url: `/pagesHelp/pages/help/help-detail?id=${item.id}&name=${item.title}&iconUrl=${vdata.data.parentData.iconUrl}`
			})
		} else {
			uni.navigateTo({
				url: `/pagesHelp/pages/help/help-game-list?id=${item.id}&name=${item.title}&iconUrl=${vdata.data.parentData.iconUrl}`
			})
		}

	};
	const onNavLeftClick = () => {
		uni.navigateBack();
	};
</script>

<style>
	page {
		background-image: url("@/static/images/yellow_bg.png");
		background-repeat: repeat;
	}
</style>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		height: 100%;
		// 设置背景图片 平铺
		// background-image: url("@/static/images/yellow_bg.png");
		// background-repeat: repeat;

		.top {
			background-color: white;
			display: flex;
			flex-direction: column;
			align-items: center;
		}

		.list-content {
			margin: 0 25rpx;
			background-color: white;
			border-radius: 35rpx;
			overflow: hidden;
			display: flex;
			flex-direction: column;

			.game-title {
				font-size: 32rpx;
				width: 100%;
				text-align: center;
			}

			.game-title-bottom-line {
				background-color: rgba(229, 229, 229, 1);
				margin: 0 70rpx;
				height: 1.74rpx;
			}

			.rules-content {
				display: flex;
				flex-direction: column;
				margin: 0 70rpx;

				.rules-item {
					display: grid;
					grid-template-columns: 1fr 1fr;
					// 网格列间距
					column-gap: 120rpx;

					.item-content {
						height: 112rpx;
						width: 100%;
						overflow: hidden;
						// 底部边框
						border-bottom: 1.74rpx solid rgba(229, 229, 229, 1);
						display: flex;
						align-items: center;
						justify-content: center;

						.item-title {
							// max-width: 100%;
							// flex: 1;
							font-size: 28rpx;
							// 省略号
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
							text-align: center;
						}
					}

				}
			}
		}
	}
</style>