<template>
	<view class="container">
		<uv-navbar placeholder="true">
			<template v-slot:left>
				<image src="/pagesHelp/static/images/back_left.png" style="width: 42rpx; height: 42rpx;"
					@click="onNavLeftClick"></image>
			</template>
			<template v-slot:center>
				<view style="display: flex; align-items: center; justify-content: center;">
					<image
						src="https://img.js.design/assets/img/6555dfe290ab84325b9e5532.png#5139c6bae75c94f15db8707e375758f0"
						style="width: 44rpx; height: 44rpx;">
					</image>
					<text style="font-size: 42rpx; font-family: AlimamaShuHeiTi;">帮助</text>
				</view>
			</template>
		</uv-navbar>

		<view class="top">
			<image :src="vdata.data.parentData.iconUrl"
				style="width: 112rpx; height: 52rpx; margin: 30rpx 0 30rpx 47rpx;"></image>
			<text
				style="margin-left: 30rpx; font-size: 28rpx;">{{ vdata.data.swiperListData[vdata.data.swiperCurrentIndex].helpListItem.title }}</text>
			<view v-if="vdata.data.catalogPickerData[0].length > 1" class="catalog" @click="showCatalogPicker">
				<text>{{ vdata.data.swiperListData[vdata.data.swiperCurrentIndex].helpListItem.title }}</text>
				<view class="arrow-down">
					<uv-icon name="arrow-down-fill" size="21rpx" color="black"></uv-icon>
				</view>
			</view>
		</view>


		<swiper class="swiper"
			:style="{height: vdata.data.swiperListData[vdata.data.swiperCurrentIndex].swiperHeight + 'px'}"
			:current="vdata.data.swiperCurrentIndex" @change="swiperChange">

			<!-- 循环创建swiper-item -->
			<swiper-item v-for="(item, index) in vdata.data.swiperListData" :key="item.helpListItem.id">
				<view :id="'swiper-item-contener' + index" class="swiper-item-contener">
					<uv-gap height="10rpx"></uv-gap>
					<view class="rule-content">
						<uv-parse class="ql-editor" :content="vdata.data.swiperListData[index].helpDetail.content"
							@ready="parseReady"></uv-parse>
					</view>
					<uv-gap height="10rpx"></uv-gap>
				</view>
			</swiper-item>

		</swiper>

		<view>
			<uv-picker ref="catalogPicker" :columns="vdata.data.catalogPickerData" keyName="title"
				@confirm="catalogPickerConfirm"></uv-picker>
		</view>

	</view>
</template>

<script setup lang="ts">
	import {
		onLoad,
	} from '@dcloudio/uni-app';
	import { getHelpDetailAPI, getHelpListAPI } from '@/services/help';
	import { HelpDetail, HelpList } from '@/types/help.d.ts'
	import { reactive, getCurrentInstance, ref } from 'vue';
	const { ctx } : any = getCurrentInstance();
	const vdata = reactive({
		data: {
			parentData: {
				iconUrl: ''
			},
			swiperListData: [{
				swiperHeight: 0,
				helpListItem: {} as HelpList,
				helpDetail: {} as HelpDetail,
			}],
			swiperCurrentIndex: 0,
			catalogPickerData: [[]] as HelpList[][]
		}
	})

	onLoad((option) => {
		initSwiperData(option);
	});

	const initSwiperData = (option : any) => {
		vdata.data.parentData.iconUrl = option.iconUrl;
		uni.showLoading({
			title: "加载中"
		});
		getHelpListAPI(option.id).then(async (res) => {
			vdata.data.swiperListData.length = 0;

			// 往swiperListData填充第一个数据
			let obj = {
				swiperHeight: 0,
				helpListItem: {} as HelpList,
				helpDetail: {} as HelpDetail,
			}
			vdata.data.swiperListData.push(obj);

			vdata.data.catalogPickerData.length = 0;
			let list : HelpList[] = [];

			// 往catalogPickerData填充第一个数据
			let allHelpList : HelpList = {
				title: option.name,
				id: '',
				parentId: '',
				version: 0,
				type: 3,
				iconUrl: '',
				tag: '',
				children: []
			}
			list.push(allHelpList);
			for (let i = 0; i < res.data.length; i++) {
				if (res.data[i].type === 3) {
					list.push(res.data[i]);
				}

				let obj1 = {
					swiperHeight: 0,
					helpListItem: res.data[i],
					helpDetail: {} as HelpDetail,
				}
				vdata.data.swiperListData.push(obj1);
			}
			vdata.data.catalogPickerData.push(list);

			getHelpDetailAPI(option.id).then((result) => {
				vdata.data.swiperListData[vdata.data.swiperCurrentIndex].helpDetail = result.data;
				vdata.data.swiperListData[vdata.data.swiperCurrentIndex].helpListItem.title = option.name;
			})
		})

	};

	const getHelpDetail = (id : string) => {
		if (vdata.data.swiperListData[vdata.data.swiperCurrentIndex].swiperHeight != 0) {
			return;
		}
		// 获取帮助列表
		uni.showLoading({
			title: "加载中"
		});
		getHelpDetailAPI(id).then(async (res) => {
			vdata.data.swiperListData[vdata.data.swiperCurrentIndex].helpDetail = res.data;
		})
	};
	const parseReady = async () => {
		// 解析完成
		const dada = await ctx.$uv.getRect(`#swiper-item-contener${vdata.data.swiperCurrentIndex}`);
		vdata.data.swiperListData[vdata.data.swiperCurrentIndex].swiperHeight = dada.height;
		uni.hideLoading();
	};

	const swiperChange = (e : any) => {
		vdata.data.swiperCurrentIndex = e.detail.current;
		getHelpDetail(vdata.data.swiperListData[e.detail.current].helpListItem.id);
	};

	const catalogPicker = ref();
	const showCatalogPicker = () => {
		if (vdata.data.catalogPickerData[0].length > 0) {
			catalogPicker.value.open();
		} else {
			uni.showToast({ icon: 'none', title: '暂无数据' })
		}
	};

	const catalogPickerConfirm = (e : any) => {
		console.log(e.indexs[0]);
		if (e.indexs[0] == vdata.data.swiperCurrentIndex) {
			return;
		}
		vdata.data.swiperCurrentIndex = e.indexs[0];
	};

	const onNavLeftClick = () => {
		uni.navigateBack();
	};
</script>

<style>
	page {
		background-image: url("@/static/images/yellow_bg.png");
		background-repeat: repeat;
	}
</style>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		height: 100%;
		// 设置背景图片 平铺
		// background-image: url("@/static/images/yellow_bg.png");
		// background-repeat: repeat;

		.top {
			background-color: white;
			display: flex;
			align-items: center;

			.catalog {
				margin-left: 65rpx;
				margin-right: 40rpx;
				flex: 1;
				width: 0;
				height: 52rpx;
				border-radius: 10rpx;
				border: 1.74rpx solid rgba(0, 0, 0, 1);
				display: flex;
				align-items: center;

				text {
					width: 0;
					flex: 1;
					font-size: 25rpx;
					margin: 0 25rpx;
				}

				.arrow-down {
					margin-right: 25rpx;
				}
			}
		}

		.swiper {
			flex: none;

			.swiper-item-contener {
				display: flex;
				flex-direction: column;

				.rule-content {
					margin: 0 20rpx;
					padding: 65rpx 40rpx;
					border-radius: 21rpx;
					background-color: rgba(204, 204, 204, 0.42);
				}
			}
		}
	}
</style>