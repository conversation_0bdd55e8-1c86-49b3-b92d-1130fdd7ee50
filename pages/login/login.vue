<script setup lang="ts">
	import { onLoad } from '@dcloudio/uni-app';
	import { loginTcg } from '@/utils/loginHelper'
	import { wxLogin } from '@/services/login';
	import { empty } from '@/uni_modules/uv-ui-tools/libs/function/test.js';
	import route from '@/uni_modules/uv-ui-tools/libs/util/route.js';


	let loginCode = ''
	onLoad(async () => {
		getLoginCode()
	})
	//获取code登录凭证
	function getLoginCode() {
		wxLogin().then(res => {
			loginCode = res
		})
	}

	// 获取用户手机号
	const onGetPhoneNumber : UniHelper.ButtonOnGetphonenumber = async (ev) => {

		if (ev.detail.errMsg === "getPhoneNumber:ok") {
			const phoneCode = ev.detail.code;
			if (empty(loginCode) || empty(phoneCode)) {
				uni.showToast({ icon: 'none', title: '登录出错,请重试' })
				getLoginCode()
				return
			}
			loginTcg({ loginCode, phoneCode }, true).then(() => {
				uni.showToast({ icon: 'success', title: '登录成功' })
				setTimeout(() => {
					console.log('页面跳转');
				}, 500)
			}).catch(() => {
				getLoginCode()
			})
		} else if (ev.detail.errMsg === "getPhoneNumber:fail user deny") {
			uni.showModal({
				title: '提示',
				content: '您已拒绝授权，如需登录，请重新点击并授权',
				showCancel: false
			});
		}
	}

	const openPrivacy = () => {
		console.log("打开隐私政策");

		// route({
		// 	url: '/pages/tcg-webView/tcg-webView',
		// 	params: {
		// 		url: 'https://mp.weixin.qq.com/wxawap/waprivacyinfo?action=show&appid=wx4feb6fed582bebc6#wechat_redirect'
		// 	}
		// })

		wx.openPrivacyContract({
			success: () => { }, // 打开成功
			fail: () => {
				uni.showToast({ icon: 'none', title: '微信版本过低,请升级您的微信版本' })
			}, // 打开失败
			complete: () => { }
		})
	}
</script>

<template>
	<view class="viewport">
		<view class="logo">
			<image src="../../static/logo.png" mode="widthFix"></image>
		</view>
		<view class="login">
			<!-- 小程序端授权登录 -->
			<button class="button phone" open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber">
				<text class="icon icon-phone"></text>
				手机号快捷登录
			</button>
			<view class="tips">登录/注册即视为你同意 <text class="privacy" @click="openPrivacy">《隐私协议》</text></view>
		</view>
	</view>
</template>

<style lang="scss">
	page {
		height: 100%;
	}

	.viewport {
		display: flex;
		flex-direction: column;
		height: 100%;
		padding: 20rpx 40rpx;
	}

	.logo {
		flex: 1;
		text-align: center;

		image {
			width: 220rpx;
			height: 220rpx;
			margin-top: 15vh;
		}
	}

	.login {
		display: flex;
		flex-direction: column;
		height: 60vh;
		padding: 40rpx 20rpx 20rpx;

		.input {
			width: 100%;
			height: 80rpx;
			font-size: 28rpx;
			border-radius: 72rpx;
			border: 1px solid #ddd;
			padding-left: 30rpx;
			margin-bottom: 20rpx;
		}

		.button {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 80rpx;
			font-size: 28rpx;
			border-radius: 72rpx;
			color: #fff;

			.icon {
				font-size: 40rpx;
				margin-right: 6rpx;
			}
		}

		.phone {
			background-color: #28bb9c;
		}

		.wechat {
			background-color: #06c05f;
		}
	}

	.tips {
		position: absolute;
		bottom: 80rpx;
		left: 20rpx;
		right: 20rpx;
		font-size: 22rpx;
		color: #999;
		text-align: center;

		.privacy {
			color: #397841;
		}
	}
</style>