<template>
	<view class="container">
		<view v-if="isPC">
			<uv-empty text="请使用微信手机端打开" textColor="black" icon="/static/images/icon_wx_qrcode.jpg"></uv-empty>
		</view>
		<view v-else>
			<view v-show="isLoading">
				<uv-loading-icon mode="circle" text="加载资源中." vertical="true" size="80rpx" text-size="30rpx"
					:textStyle="loadingTextStyle"></uv-loading-icon>
			</view>
			<view v-show="!isLoading" @click="onEmptyClick" style="padding: 0 40rpx;">
				<uv-empty :text="emptyText" mode="data"></uv-empty>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import {
		onLoad,
	} from '@dcloudio/uni-app';
	import {
		ref,
	} from 'vue';
	import { isLogin, loginTcgWithLoginCode } from '@/utils/loginHelper'
	import { loginIM, scanQRCodeAPI } from '@/services/login';
	import { TcgHttpError } from '@/utils/http';
	import { useMemberStore } from '@/stores/index';
	import { empty } from '@/uni_modules/uv-ui-tools/libs/function/test.js';

	import { downLoadAlimamaShuHeiTiFont, downLoadYouSheBiaoTiHeiFont, downLoadDeYiHeiFont, downLoadBerkshireSwashFont, downLoadPassionOneFont } from "@/services/font";

	const loadingTextStyle = {
		marginTop: '20rpx',
	};
	const emptyText = ref('出错啦,请点击重试!');
	const isLoading = ref(true);
	// 是否是下载字体失败
	const isDownloadFontFail = ref(false);
	const qrCodeUrl = ref('');
	// 定义isPC变量,uni.getDeviceInfo().platform是不是等于'windows'或者'mac'
	let isPC = uni.getDeviceInfo().platform == 'windows' || uni.getDeviceInfo().platform == 'mac';
	onLoad(() => {
		console.log('客户端平台', uni.getDeviceInfo().platform);
		// 禁止PC端打开
		if (isPC) {
			return
		}
		if (!empty(uni.getEnterOptionsSync().query.q) && uni.getEnterOptionsSync().query.q.includes("www.duelchannel.com") && uni.getEnterOptionsSync().query.q.includes("grcode")) {
			console.log("微信扫码二维码获取的链接1", decodeURIComponent(uni.getEnterOptionsSync().query.q));
			qrCodeUrl.value = decodeURIComponent(uni.getEnterOptionsSync().query.q)
		}
		beginLogin()
	});


	const beginLogin = () => {
		if (isLogin()) {
			scanQRCode()
			loginIm()
		} else {
			loginTcgOnlyLoginCode()
		}
	}

	const onEmptyClick = () => {
		isLoading.value = true
		if (isDownloadFontFail.value) {
			downloadFont()
		} else {
			beginLogin()
		}
	}
	const downloadFont = () => {
		// 使用Promise.all来并行下载字体，提高性能
		Promise.all([
			downLoadAlimamaShuHeiTiFont(),
			downLoadYouSheBiaoTiHeiFont(),
			downLoadDeYiHeiFont(),
			downLoadBerkshireSwashFont(),
			downLoadPassionOneFont()
		]).then(() => {
			uni.redirectTo({
				url: '/pagesIndex/pages/index/index'
			})
		}).catch(() => {
			isDownloadFontFail.value = true
			isLoading.value = false
		})
	}

	const loginIm = () => {
		loginIM({
			userID: useMemberStore().profile.uid!,
			userSig: useMemberStore().profile.userSig!
		}).then(() => {
			downloadFont()
		}).catch(() => {
			// IM登录失败有可能是userSig过期了，所以这里重新登录一次拿到最新的userSig
			loginTcgOnlyLoginCode()
		})
	}

	const loginTcgOnlyLoginCode = () => {
		loginTcgWithLoginCode(false).then(() => {
			scanQRCode()
			console.log("登录TCG进入首页");
			downloadFont()
		}).catch((e) => {
			isDownloadFontFail.value = false
			isLoading.value = false
			emptyText.value = '登录失败,请点击重试!'
			if (e instanceof TcgHttpError) {
				let error = e as TcgHttpError
				if (error.data.code == 9527) {
					emptyText.value = error.data.msg
				}
			}
		})
	}

	const scanQRCode = () => {
		if (empty(qrCodeUrl.value)) {
			console.log("没有二维码链接");
			return
		}
		const uuid = qrCodeUrl.value.split('/').pop();
		console.log("微信扫码二维码获取的uuid", uuid);
		scanQRCodeAPI({
			uuid: uuid
		}).then(res => {
			qrCodeUrl.value = ''
		})
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		justify-content: center;
		height: 100vh;
	}
</style>