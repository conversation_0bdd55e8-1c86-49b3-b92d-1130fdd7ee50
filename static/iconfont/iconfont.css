@font-face {
	font-family: "custom-icon";
	/* Project id 4343735 */
	src:
		url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAASwAAsAAAAACTgAAARkAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHFQGYACDCAqFSIUfATYCJAMQCwoABCAFhQsHURtACFGU7k2D7CMhN9F6iaVhcoUS4mp4HQ9ve7+d+2Z+m7elq1KqW7tZuR0UkqBKEw6HgnjiUSjT42tzKNwf/j3zVUNyRg4sdNYApMlLmlubdOnrwHRzMuFTxF+x+Q/+Eqa/X+lP3ALLRwsg2UkL9m1rvG48ugjvQO1W6RpeGE5qlh79GoFiWwP1YUf/4SDqJol4kEZLEIOwPG5yLAl63Q2ygRba6ib21CyegAbVI/VDPHZ/P/7ajV5UGom4yumNdhpcfa37XyP9l0jv2ASB7l6hgYT1jpni0uw4pvET6zQK5NCaZfEsqFRsC7r9j0cimoSih+L7ycQPoAWTqXhLOamEf+IFQfMpbRT8J64AoRrnj8yS1Ns7FVEYmVFkiWRX5FDIHw4rwaAUCIyV+eFSMKiEw/5QaIa8SaXyjwrLw3AlOGSjtEFOuhzC5OEBogUgVitg8ib/ZhxXQCnrKO7geUXjQApWB8y7SZfwSOWAfz8WIZehHFeDsLZNIKby75f3AsWiS/twHMSVAyoVjs6ZHRp5wXykrC0xeYO0EVceXZOuPgz1IO7WYBA4Hcpmld9xvuJCjXnNh6pPVp31BbA0LCrwjcnJt4JIQig0tumqx3NqHNVVJFJcx4hIn0urqmcY++j1fUdzLtWM4V+fEh+Ff8abKOsIy9Ch5uFWus/iSvu0xH+JzEPR3Diyq7SzpLijtM+IJvzz1ymfsc+JWobbh4LH7nFFaR9Oqq1w+Gpouma64jqXqrPrx/CtqU46ozmrrX/2w2xd9KF3aLQu55Oi1oKm5sLWohF8scWNSAcrriMdQqWFPly409Wnt/motsy2KJ5H98ZOBpU7CdaO/pnJbbNp8/DBNksLNs3dkdk3c2cu9qkGhdY0sE31Qgv2MnOLyVXTprz6U0F6q4cr8z2PIGsaNAfOvVpxuHlkLluV82YvsRo9N2Qop6+2WGKmwtLBQ/RcDXSJqhy2eeSX3YZ+en2/Mfq+/QxjFNeRjokc1U8PPAC6bzRbNCXu3v80TzUFAJCeqX+eTrt5rkQpk6Lrvvaqt+nY503vbjB/940WFCvSVoEWnDzKa8iR+RskZ0iiRSek8npEUEVbUdAFwjPkJXUNA6Et1HchBZWWSZDUFuCMb8MN/ABu4kegWDOEX9nec2hE7mDRagrCmFOg0ucFSMY8gTP+BWqQQgc38XRQXA3r5WqLsbRVhCSCNOGYSXCUwDOqDlE+LxLcxv/ioZD1uUjx2R1ER0LRy9FxwmqyAOlOyEOxvCO801gbQgzBiIKb6Kj2btDlEgiPKHRDCpmcCHlqzGYG+hETJbhBKxFEQiAawWEmAoci4DH2RQjFxwsRuBkNf2coiOXjQhLNQwCxkSDxruDYxhCsTCw4uFPBY/t28GivaVg2CMIgMJxfzEXo6LINcsmEgOCBd+oGURATZ4GYRw0zG8VgFaZmVe4VPgeFuESOKlLkaOh94CR5yumzNxFmCr5IxJF8txbGtunJiBzkaS8A') format('woff2');
}

.custom-icon {
	font-family: "custom-icon" !important;
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.custom-icon-shanchuhaoyou:before {
	content: "\100f2";
}

.custom-icon-tianjiahaoyou:before {
	content: "\100d0";
}

.custom-icon-friends:before {
	content: "\e600";
}