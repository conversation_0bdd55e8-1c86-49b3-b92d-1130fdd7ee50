<template>
	<view class="container">
		<uv-gap height="30rpx"></uv-gap>
		<view class="input-bg">
			<view class="input">
				<uv-input placeholder="请输入您的昵称" v-model="nickname" border="none" clearable="true" fontSize="30rpx"
					type="nickname" maxlength="10"></uv-input>
			</view>
		</view>
		<uv-gap height="30rpx"></uv-gap>
		<view class="confirm" @click="nicknameConfirm">
			确定
			<!-- <uv-button type="success" text="确定" @click="nicknameConfirm"></uv-button> -->
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref } from 'vue';
	import { useMemberStore } from '@/stores/index';
	import { empty } from '@/uni_modules/uv-ui-tools/libs/function/test.js';
	import { updateMemberProfileAPI } from '@/services/profile';
	const nickname = ref(useMemberStore().profile.nickName);
	const nicknameConfirm = async () => {
		if (empty(nickname.value)) {
			uni.showToast({
				icon: "none",
				title: "昵称不能为空"
			})
			return
		}
		uni.showLoading({
			title: ""
		})
		updateMemberProfileAPI({ nickName: nickname.value }).then(() => {
			uni.hideLoading()
			useMemberStore().profile.nickName = nickname.value;
			uni.showToast({
				icon: "success",
				title: "修改昵称成功"
			})
			//延迟1500毫秒返回上一页
			setTimeout(() => {
				uni.navigateBack()
			}, 1000);
		})
	}
</script>

<style scoped lang="scss">
	.container {
		width: 100%;
		height: 100vh;
		background-image: url("@/static/images/yellow_bg.png");
		background-repeat: repeat;
		display: flex;
		flex-direction: column;

		.input-bg {
			background-color: rgba(56, 56, 56, 1);
			border-radius: 28rpx 49rpx 66rpx 49rpx;
			margin: 0 40rpx;
			min-height: 190rpx;
			display: flex;

			.input {
				flex: 1;
				width: 0;
				margin: 12rpx 17rpx 26rpx 9rpx;
				border-radius: 40rpx 44rpx 66rpx 49rpx;
				background-color: white;
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 0 20rpx;
			}
		}

		.confirm {
			height: 80rpx;
			line-height: 80rpx;
			color: white;
			text-align: center;
			margin: 0 40rpx;
			border-radius: 10rpx;
			background: url('https://img.js.design/assets/img/6561c44bd0123125685b5dbd.png#b9422d45dd40939b4a081fda11087d5d');
		}
	}
</style>