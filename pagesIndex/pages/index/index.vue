<script setup lang="ts">
	import chooseGames from '@/pagesIndex/pages/index/components/choose-games.vue'
	import playerKillingList from '@/pagesIndex/pages/index/components/player-killing-list.vue'
	import playerCreateRoom from '@/pagesIndex/pages/index/components/player-create-room.vue'
	import playerCreateRoomExpand from '@/pagesIndex/pages/index/components/player-create-room-expand.vue'
	import RoomFightInvite from '@/pagesIndex/pages/index/components/room-fight-invite.vue'

	import { getGameListAPI } from '@/services/game'
	import { getInviteMessageAPI, joinRoomAPI, createRoomAPI, inviteJoinRoomAPI, matchRoomAPI, getUserOnlineRoomAPI, getRoomDetailAPI } from '@/services/room'

	import type { RoomDetail, RoomListItem } from '@/types/room.d.ts';
	// import { isLogin, loginTcgWithLoginCode } from '@/utils/loginHelper'
	import { useMemberStore } from '@/stores/index'
	import { useGamesStore } from '@/stores/modules/games'
	import { useShareStore } from '@/stores/modules/share'
	import { useRoomStore } from '@/stores/modules/room'
	import { useWatchRoomStore } from '@/stores/modules/watchRoom'

	import type { UserLikeVO } from '@/types/member.d.ts'

	import {
		onLoad,
		onUnload,
		onShow,
		onHide
	} from '@dcloudio/uni-app';
	import {
		getCurrentInstance,
		reactive,
		ref,
		computed,
		ComputedRef,
		provide,
	} from 'vue';
	import { FriendItem } from '@/types/friend.d.ts'
	import { getMessageListAPI, setReadMessageAPI } from '@/services/message'
	import { FriendApplyItem } from '@/types/message.d.ts'
	import { useMessageStore } from '@/stores/modules/message'
	import { tcgAppOnShowRoute } from '@/utils/tcgRouteHelper';
	import { pages } from '@/uni_modules/uv-ui-tools/libs/function/index.js';

	const {
		ctx
	} : any = getCurrentInstance();
	let inviteRoomList : RoomListItem[] = reactive([])
	onShow(() => {
		fromShareEnterRoom()
		getUserOnlineRoomFromRoomStore()
		getInviteMessage()
	});
	onHide(() => {
		console.log("首页执行了onHide");
	});
	onLoad(() => {
		// 判断是否第一次打开小程序
		isFirstOpenApp()
		getGameList()
		getUserOnlineRoomFromApi()
		getUnReadMessageNum()
		addListener()
	});
	onUnload(() => {
		removeListener()
	});

	// 注册监听事件
	const addListener = () => {
		uni.$on('inviteRoomListUpdate', onInviteRoomListUpdate)
		uni.$on('onTcgWebEnterWatchLive', onTcgWebEnterWatchLive)
		uni.$on('onTcgWebEndWatchLive', onTcgWebEndWatchLive)
		uni.$on('TcgAppOnShow', tcgAppOnShow)
		uni.$on('onAccountIsBanned', onAccountIsBanned)

	}
	// 移除监听事件
	const removeListener = () => {
		uni.$off('inviteRoomListUpdate', onInviteRoomListUpdate);
		uni.$off('onTcgWebEnterWatchLive', onTcgWebEnterWatchLive)
		uni.$off('onTcgWebEndWatchLive', onTcgWebEndWatchLive)
		uni.$off('TcgAppOnShow', tcgAppOnShow)
		uni.$off('onAccountIsBanned', onAccountIsBanned)
	}

	const tcgAppOnShow = () => {
		console.log("首页-小程序从后台进入前台啦");
		getUnReadMessageNum();
		tcgAppOnShowRoute()
	}

	const onTcgWebEnterWatchLive = () => {
		watchRoomStore.setIsShowWatchLive(true)
	}
	const watchRoomStore = useWatchRoomStore();

	const onTcgWebEndWatchLive = () => {
		watchRoomStore.setIsShowWatchLive(false)
	}
	const accountIsBanned = ref(false);
	const accountBannedMsg = ref('');
	const onAccountIsBanned = (data : any) => {
		if (data.isBanned) {
			// 清除缓存 返回首页
			const roomStore = useRoomStore();
			roomStore.clearRoomDetail();
			uni.navigateBack({
				delta: 100
			})
		}
		accountIsBanned.value = data.isBanned;
		accountBannedMsg.value = data.msg;
	}


	// 刷新邀请列表
	const onInviteRoomListUpdate = () => {
		getInviteMessage()
	}
	const getGameList = async () => {
		const data = await getGameListAPI();
		const gamesStore = useGamesStore();
		gamesStore.setGameList(data.data);
	}
	const getInviteMessage = async () => {
		const data = await getInviteMessageAPI({
			gameTypeId: pageGameInfo.value.gameTypeId!,
			gameId: pageGameInfo.value.gameId!
		});
		inviteRoomList.length = 0;
		inviteRoomList.push(...data.data);

		if (inviteRoomList.length > 3) {
			inviteRoomList.splice(3);
		}
	}
	const getUnReadMessageNum = () => {
		getMessageListAPI({ status: 1 }).then(res => {
			useMessageStore().setUnreadMessageNum(res.data.total);
		})
	}

	const getUserOnlineRoomFromRoomStore = async () => {
		const shareParams = useShareStore().shareParams;
		const userUid = useMemberStore().profile.uid;
		if (!ctx.$uv.test.empty(shareParams.roomId) && shareParams.roomOwnerUid !== userUid) {
			return;
		}
		const roomStore = useRoomStore();
		if (roomStore.roomDetail.status == 1 || roomStore.roomDetail.status == 2 || roomStore.roomDetail.status == 3) {
			let roomDetail = encodeURIComponent(JSON.stringify(roomStore.roomDetail));
			ctx.$uv.route({
				url: '/pagesRoom/room/room',
				params: {
					roomDetail: roomDetail
				}
			})
		}
	}

	const getUserOnlineRoomFromApi = async () => {
		const shareParams = useShareStore().shareParams;
		const userUid = useMemberStore().profile.uid;
		if (!ctx.$uv.test.empty(shareParams.roomId) && shareParams.roomOwnerUid !== userUid) {
			return;
		}
		getUserOnlineRoomAPI().then(res => {
			if (!ctx.$uv.test.empty(res.data)) {

				let user = res.data.userList.find(user => user.uid == userUid);
				if (!ctx.$uv.test.empty(user)) {
					if (user.userType == 2) {
						watchRoomStore.setIsShowWatchLive(true)
						return
					}
				}

				if (res.data.status == 1 || res.data.status == 2 || res.data.status == 3) {
					let roomDetail = encodeURIComponent(JSON.stringify(res.data));
					ctx.$uv.route({
						url: '/pagesRoom/room/room',
						params: {
							roomDetail: roomDetail
						}
					})
				}
			}
		})

	}


	const fromShareEnterRoom = async () => {
		if (ctx.$uv.test.empty(useShareStore().shareParams.roomId) && ctx.$uv.test.empty(useShareStore().shareParams.roomOwnerUid)) {
			return
		}

		if (useShareStore().shareParams.roomOwnerUid === useMemberStore().profile.uid) {
			return
		}

		// 如果不在房间页面,判断页面数组里面有没有房间页面,有的话先返回到房间页面,然后刷新房间信息
		const pageArray = pages();
		for (let i = 0; i < pageArray.length; i++) {
			const page = pageArray[i];
			if (page.route == "pagesRoom/room/room") {
				uni.showToast({
					title: '您已经在房间里了,请先退出房间后,在加入',
					icon: 'none',
				});
				return;
			}
		}


		joinRoomAPI(useShareStore().shareParams.roomId, 3).then(res => {
			let roomDetail = encodeURIComponent(JSON.stringify(res.data));
			ctx.$uv.route({
				url: '/pagesRoom/room/room',
				params: {
					roomDetail: roomDetail
				}
			})
		}).finally(() => {
			useShareStore().clearShareParams();
		})
	}

	const createRoom = async () => {

		uni.showLoading({
			title: ''
		})
		createRoomAPI({
			type: 2,
			gameTypeId: pageGameInfo.value.gameTypeId!,
			gameId: pageGameInfo.value.gameId!,
			watchType: 1
		}).then(res => {
			uni.hideLoading()
			let roomDetail = encodeURIComponent(JSON.stringify(res.data));
			ctx.$uv.route({
				url: '/pagesRoom/room/room',
				params: {
					roomDetail: roomDetail
				}
			})
		})
	}

	const matchRoom = async () => {

		uni.showLoading({
			title: ''
		})
		matchRoomAPI({
			gameTypeId: pageGameInfo.value.gameTypeId!,
			gameId: pageGameInfo.value.gameId!
		}).then(res => {
			uni.hideLoading()
			let roomDetail = encodeURIComponent(JSON.stringify(res.data));
			ctx.$uv.route({
				url: '/pagesRoom/room/room',
				params: {
					roomDetail: roomDetail
				}
			})
		})
	}


	// 用户选择的游戏信息
	const userChooseGameInfo : UserLikeVO = reactive({});
	// 界面上最终显示的游戏信息
	const pageGameInfo : ComputedRef<UserLikeVO> = computed(() => {
		// 如果用户选择了游戏,则显示用户选择的游戏
		if (!ctx.$uv.test.empty(userChooseGameInfo)) {
			return {
				gameId: userChooseGameInfo.gameId,
				gameName: userChooseGameInfo.gameName,
				img: userChooseGameInfo.img,
				gameTypeName: userChooseGameInfo.gameTypeName,
				gameTypeId: userChooseGameInfo.gameTypeId,
			}
		}
		// 如果用户没有选择游戏,则显示用户喜欢的游戏
		if (useMemberStore().profile.gameId) {
			return {
				gameId: useMemberStore().profile.gameId,
				gameName: useMemberStore().profile.gameName,
				img: useMemberStore().profile.img,
				gameTypeName: useMemberStore().profile.gameTypeName,
				gameTypeId: useMemberStore().profile.gameTypeId,
			}
		}
		// 如果用户没有喜欢的游戏,则显示游戏列表第一个游戏

		let gameId = '';
		let gameName = '';
		let img = '';
		let gameTypeName = '';
		let gameTypeId = '';
		if (useGamesStore().gameList.length > 0) {
			gameId = useGamesStore().gameList[0].gameId;
			gameName = useGamesStore().gameList[0]?.gameName;
			img = useGamesStore().gameList[0]?.img;
			gameTypeName = useGamesStore().gameList[0].types[0]?.typeName;
			gameTypeId = useGamesStore().gameList[0].types[0]?.typeId;
		}

		return {
			gameId,
			gameName,
			img,
			gameTypeName,
			gameTypeId,
		}
	})

	const tcgChooseGamePopop = ref()
	const isShowChooseGamePopop = ref(false)

	const handleChooseGameCancel = () => {
		tcgChooseGamePopop.value.close();
		setTimeout(() => {
			isShowChooseGamePopop.value = false
		}, 20);
	}
	const handleChooseGameConfirm = (chooseGame : UserLikeVO) => {
		tcgChooseGamePopop.value.close();
		setTimeout(() => {
			isShowChooseGamePopop.value = false
		}, 20);
		userChooseGameInfo.gameId = chooseGame.gameId;
		userChooseGameInfo.gameName = chooseGame.gameName;
		userChooseGameInfo.img = chooseGame.img;
		userChooseGameInfo.gameTypeId = chooseGame.gameTypeId;
		userChooseGameInfo.gameTypeName = chooseGame.gameTypeName;
	}

	const changeGame = () => {
		tcgChooseGamePopop.value.open()
		isShowChooseGamePopop.value = true
	}

	const matchGameBtnClick = async () => {
		matchRoom()
	}
	const createRoomBtnClick = () => {
		createRoom()
	}
	// 对战列表是否展开
	const tcgFightInvitePopupRef = ref();
	const isShowTcgFightInvitePopup = ref(false)
	const fightInviteClick = () => {
		tcgFightInvitePopupRef.value.open()
		isShowTcgFightInvitePopup.value = true
	}

	const tcgFightInvitePopupCloseBtnClick = () => {
		tcgFightInvitePopupRef.value.close()
		setTimeout(() => {
			isShowTcgFightInvitePopup.value = false
		}, 300);
	}

	const onDuelClick = async (item : RoomListItem) => {
		uni.showLoading({
			title: ''
		})
		const joinType = item.hallType == 1 ? 1 : 0;
		joinRoomAPI(item.rid!, joinType).then(res => {
			uni.hideLoading()
			let roomDetail = encodeURIComponent(JSON.stringify(res.data));
			ctx.$uv.route({
				url: '/pagesRoom/room/room',
				params: {
					roomDetail: roomDetail
				}
			})
		}).finally(() => {
			getInviteMessage()
		})
	}
	const onHomeInviteFriend = async (item : FriendItem) => {

		uni.showLoading({
			title: ''
		})

		createRoomAPI({
			type: 1,
			gameTypeId: pageGameInfo.value.gameTypeId!,
			gameId: pageGameInfo.value.gameId!,
			watchType: 2
		}).then(res => {
			uni.hideLoading()
			let roomDetail = encodeURIComponent(JSON.stringify(res.data));
			ctx.$uv.route({
				url: '/pagesRoom/room/room',
				params: {
					roomDetail: roomDetail
				}
			})
			inviteJoinRoom(res.data, item)
		})
	}

	const inviteJoinRoom = async (roomDetail : RoomDetail, item : FriendItem) => {
		inviteJoinRoomAPI(roomDetail.rid!, item.detail.uid!).then(() => {
			uni.showToast({
				title: "已给好友发送邀请",
				icon: 'none'
			})
		})
	}


	const onDeleteFriendInviteClick = async (item : RoomListItem) => {
		setReadMessageAPI([item.messageId]).then(() => {
			// 把item从inviteRoomList中删除
			const index = inviteRoomList.findIndex((inviteItem) => inviteItem.messageId == item.messageId);
			if (index !== -1) {
				inviteRoomList.splice(index, 1);
			}
		})
	}
	const onReloadListClick = async () => {
		uni.showLoading({
			title: ''
		})
		await getInviteMessage()
		uni.hideLoading()
	}

	const tcgModalPopup = ref();
	// isDeleteFriend  : 是否删除好友
	const modalMessageType = ref('');
	// 是否删除好友
	const modalMessage = ref('');

	const onCancelTcgMoadlPopup = (modalMessageType : string) => {
		console.log(modalMessageType);
		tcgModalPopup.value.close();
	}
	const onConfirmTcgMoadlPopup = (modalMessageType : string) => {
		tcgModalPopup.value.close();
		// 延迟300毫秒,等待动画结束
		setTimeout(() => {
			if (modalMessageType == 'isDeleteFriend') {
				if (deleteFriendCallback) {
					deleteFriendCallback(deleteFriendItem); // 调用回调时传递参数
				}
			} else if (modalMessageType == 'isMaskingFriend') {
				if (maskingFriendCallback) {
					maskingFriendCallback(maskingFriendItem); // 调用回调时传递参数
				}
			} else if (modalMessageType == 'isAgreeFriendRequest') {
				if (friendRequestCallback) {
					friendRequestCallback(friendRequestItem, "1"); // 调用回调时传递参数
				}
			} else if (modalMessageType == 'isRefuseFriendRequest') {
				if (friendRequestCallback) {
					friendRequestCallback(friendRequestItem, "2"); // 调用回调时传递参数
				}
			}
		}, 300)
	}




	let deleteFriendCallback : DeleteFriendtCallbackFunction;
	let deleteFriendItem : FriendItem;
	// 定义接受一个参数的回调函数类型
	type DeleteFriendtCallbackFunction = (item : FriendItem) => void;
	const handleDeleteFriend = (item : FriendItem, callback : DeleteFriendtCallbackFunction) => {
		deleteFriendCallback = callback;
		deleteFriendItem = item;
		modalMessageType.value = 'isDeleteFriend';
		modalMessage.value = '是否删除好友';
		tcgModalPopup.value.open();
	}
	provide('handleDeleteFriend', handleDeleteFriend);


	let maskingFriendCallback : DeleteFriendtCallbackFunction;
	let maskingFriendItem : FriendItem;
	// 定义接受一个参数的回调函数类型
	type MaskingFriendtCallbackFunction = (item : FriendItem) => void;
	const handleMaskingFriend = (item : FriendItem, callback : MaskingFriendtCallbackFunction) => {
		maskingFriendCallback = callback;
		maskingFriendItem = item;
		modalMessageType.value = 'isMaskingFriend';
		modalMessage.value = '是否拉黑好友';
		tcgModalPopup.value.open();
	}
	provide('handleMaskingFriend', handleMaskingFriend);


	let friendRequestCallback : FriendRequestCallbackFunction;
	let friendRequestItem : FriendApplyItem;
	// 定义接受一个参数的回调函数类型
	type FriendRequestCallbackFunction = (item : FriendApplyItem, type : string) => void;
	const handleFriendRequest = (item : FriendApplyItem, type : string, callback : FriendRequestCallbackFunction) => {
		friendRequestItem = item;
		friendRequestCallback = callback;
		if (type == '1') {
			modalMessageType.value = 'isAgreeFriendRequest';
			modalMessage.value = '是否同意好友申请';
		} else if (type == '2') {
			modalMessageType.value = 'isRefuseFriendRequest';
			modalMessage.value = '是否拒绝好友申请';
		}
		tcgModalPopup.value.open();
	}

	provide('handleFriendRequest', handleFriendRequest);

	const isShowTcgInstructions = ref(false)
	const isFirstOpenApp = () => {
		// 从storage中获取是否第一次打开小程序
		const isFirstOpenApp = uni.getStorageSync('isFirstOpenApp');
		if (!isFirstOpenApp) {
			isShowTcgInstructions.value = true
		}
	}
	const onInstructionsClick = () => {
		isShowTcgInstructions.value = false
		uni.setStorageSync('isFirstOpenApp', true);
	}
</script>

<template>
	<view class="container">

		<tcg-header @onHomeInviteFriend="onHomeInviteFriend"></tcg-header>

		<view style="flex: 1;"></view>
		<choose-games @changeGame="changeGame" :pageGameInfo="pageGameInfo"></choose-games>
		<view style="flex: 1;"></view>
		<player-create-room @matchGameBtnClick="matchGameBtnClick"
			@createRoomBtnClick="createRoomBtnClick"></player-create-room>
		<view style="flex: 1;"></view>
		<player-killing-list :inviteRoomList="inviteRoomList" @fightInviteClick="fightInviteClick"
			@onDuelClick="onDuelClick" @onDeleteFriendInviteClick="onDeleteFriendInviteClick"
			@onReloadListClick="onReloadListClick"></player-killing-list>
		<view style="flex: 1;"></view>

		<!-- <template v-if="isFightListExpand">
			<view style="flex: 1;"></view>
			<choose-games @changeGame="changeGame" :pageGameInfo="pageGameInfo"></choose-games>
			<view style="flex: 1;"></view>
			<player-create-room @matchGameBtnClick="matchGameBtnClick"
				@createRoomBtnClick="createRoomBtnClick"></player-create-room>
			<view style="flex: 1;"></view>
			<player-killing-list :inviteRoomList="inviteRoomList" @fightInviteClick="fightInviteClick"
				@onDuelClick="onDuelClick" @onDeleteFriendInviteClick="onDeleteFriendInviteClick"
				@onReloadListClick="onReloadListClick"></player-killing-list>
			<view style="flex: 1;"></view>
		</template>
		<template v-else>
			<view style="flex: 2;"></view>
			<choose-games @changeGame="changeGame" :pageGameInfo="pageGameInfo"></choose-games>
			<view style="flex: 2;"></view>
			<player-create-room-expand @matchGameBtnClick="matchGameBtnClick"
				@createRoomBtnClick="createRoomBtnClick"></player-create-room-expand>
			<view style="flex: 2;"></view>
			<room-fight-invite @fightInviteClick="fightInviteClick"></room-fight-invite>
			<view style="flex: 1;"></view>
		</template> -->
	</view>

	<view>
		<uv-popup :overlay-style="{background: 'rgba(56, 56, 56, 0.8)'}" ref="tcgModalPopup" bgColor="none"
			:safeAreaInsetBottom="false" mode="center" :closeOnClickOverlay="false">
			<tcg-modal :message="modalMessage" :messageType="modalMessageType" @cancel="onCancelTcgMoadlPopup"
				@confirm="onConfirmTcgMoadlPopup"></tcg-modal>
		</uv-popup>
	</view>

	<view>
		<uv-popup :overlay-style="{background: 'rgba(56, 56, 56, 0.8)'}" ref="tcgChooseGamePopop" bgColor="none"
			:safeAreaInsetBottom="false" mode="center" duration="0" @maskClick="handleChooseGameCancel">

			<tcg-choose-game-modal v-if="isShowChooseGamePopop" :pageGameInfo="pageGameInfo"
				@cancel="handleChooseGameCancel" @confirm="handleChooseGameConfirm"></tcg-choose-game-modal>
		</uv-popup>
	</view>

	<view>
		<uv-popup :overlay-style="{background: 'rgba(56, 56, 56, 0.8)'}" ref="tcgFightInvitePopupRef" bgColor="none"
			:safeAreaInsetBottom="false" mode="center" :closeOnClickOverlay="false">

			<tcg-fight-invite-popup v-if="isShowTcgFightInvitePopup"
				@closeBtnClick="tcgFightInvitePopupCloseBtnClick"></tcg-fight-invite-popup>
		</uv-popup>
	</view>

	<!-- 是否显示平台使用说明 -->
	<tcg-instructions :isShowTcgInstructions="isShowTcgInstructions"
		@onInstructionsClick="onInstructionsClick"></tcg-instructions>

	<view>
		<uv-overlay :show="watchRoomStore.isShowWatchLive" :opacity="0.8" z-index="99998">
			<view style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">
				<text style="color: white;">电脑观战中...</text>
			</view>
		</uv-overlay>
	</view>

	<view>
		<uv-overlay :show="accountIsBanned" :opacity="0.8" z-index="99999">
			<view style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">
				<text style="color: white; padding: 0 40rpx;">{{ accountBannedMsg }}</text>
			</view>
		</uv-overlay>
	</view>


</template>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		// 设置背景图片 平铺
		background-image: url("@/static/images/yellow_bg.png");
		background-repeat: repeat;
	}
</style>