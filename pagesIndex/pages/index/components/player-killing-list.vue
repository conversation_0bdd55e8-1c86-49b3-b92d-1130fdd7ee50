<template>
	<view class="container">

		<!-- 对战邀请列表 -->
		<view class="player-killing-list">
			<!-- 对战列表上部分 -->
			<view class="player-killing-list-top">
				<view class="player-killing-list-top-container">
					<view class="player-killing-list-top-invite" @click="handleFightInviteClick">
						<text>对战邀请</text>
					</view>
					<view class="reload" @click="handleReloadListClick">
						<uv-icon name="reload" color="black" size="35rpx"></uv-icon>
					</view>
				</view>

			</view>

			<view class="player-killing-list-bottom" v-for="(item, index) in props.inviteRoomList" :key="index">
				<!-- 间隙: 左边黑和右边绿 -->
				<view style="display: flex; flex-direction: row; height: 15rpx;">
					<view style="width: 100rpx; background-color: #383838;"></view>
					<view style="background-color: #CBD526;flex: 1;"></view>
				</view>
				<view class="player-killing-list-item">
					<view class="player-killing-list-item-left"
						:style="{backgroundColor: item.hallType === 1 ? '#AFDF4B' : '#FF8D1A'}"
						@click="handleDeleteFriendInviteClick(item)">
						{{item.hallType === 1 ? "好友" : "大厅"}}

					</view>
					<view class="player-killing-list-item-middle">
						<!-- 头像 -->
						<view class="avatar">
							<uv-image :src="item.ownerAvatar" width="45rpx" height="45rpx" shape="circle"></uv-image>
						</view>
						<!-- 昵称 -->
						<view class="nickname">
							{{item.ownerNickName}}
						</view>
						<!-- mode -->
						<view class="mode">
							{{item.gameName}}
						</view>
					</view>
					<!-- <view class="player-killing-list-item-right" @click="handleOnDuelClick(item)">
						Duel！
						
					</view> -->
					<image class="player-killing-list-item-right" src="@/static/images/icon_duel_start.png"
						mode="widthFix" @click="handleOnDuelClick(item)"></image>
				</view>
			</view>
		</view>
	</view>

</template>

<script setup lang="ts">
	import type { RoomListItem } from '@/types/room.d.ts';
	const props = defineProps<{ inviteRoomList : RoomListItem[] }>()

	const emit = defineEmits(["fightInviteClick", "onDuelClick", "onDeleteFriendInviteClick", "onReloadListClick"]);
	function handleFightInviteClick() {
		emit("fightInviteClick");
	}
	function handleOnDuelClick(item : RoomListItem) {
		emit("onDuelClick", item);
	}
	function handleDeleteFriendInviteClick(item : RoomListItem) {
		if (item.hallType == 1) {
			emit("onDeleteFriendInviteClick", item);
		}
	}
	function handleReloadListClick() {
		emit("onReloadListClick");
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;

		.player-killing-list {
			background-color: $color-CBD526;
			margin-left: 20rpx;
			margin-right: 20rpx;
			border-radius: 30rpx 20rpx 30rpx 30rpx;
			display: flex;
			flex-direction: column;
			overflow: hidden;

			.player-killing-list-top {
				background-color: $color-383838;
				border-radius: 0rpx 0rpx 40rpx 0;

				.player-killing-list-top-container {
					background-color: $color-AFDF4B;
					margin: 10rpx 15rpx 20rpx 10rpx;
					border-radius: 30rpx 20rpx 40rpx 15rpx;
					display: flex;
					flex-direction: column;
					overflow: hidden;

					.player-killing-list-top-invite {
						border-radius: 0rpx 0rpx 40rpx 15rpx;
						// 设置背景图片
						background-image: url("@/static/images/icon_fightInviteBtn_bg.png");
						background-size: cover;
						text-align: center;
						padding: 20rpx 0;
						font-family: YouSheBiaoTiHei;

						text {
							font-size: 42rpx;
						}
					}

					.reload {
						display: flex;
						justify-content: center;
						padding: 10rpx 0;
					}
				}
			}

			.player-killing-list-bottom {
				display: flex;
				flex-direction: column;

				.player-killing-list-item {
					background-color: $color-383838;
					display: flex;
					flex-direction: row;
					justify-content: space-between;
					align-items: center;

					.player-killing-list-item-left {
						margin: 0 20rpx;
						width: 60rpx;
						height: 60rpx;
						background-color: $color-AFDF4B;
						font-size: 18rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						border-radius: 15rpx 23rpx;
						font-family: YouSheBiaoTiHei;
					}

					.player-killing-list-item-middle {
						flex: 1;
						margin: 10rpx 0rpx 10rpx 25rpx;
						background-color: white;
						display: flex;
						align-items: center;
						border-radius: 35rpx 18rpx 35rpx 18rpx;

						.avatar {
							margin: 15rpx;
						}

						.nickname {
							padding: 5rpx 0rpx;
							background-color: $color-EFEFEF;
							border-radius: 8rpx;
							font-size: 18rpx;
							flex: 2;
							// 文字居中
							text-align: center;
							font-family: AlimamaShuHeiTi;
						}

						.mode {
							margin: 0 15rpx;
							padding: 5rpx 0rpx;
							background-color: $color-EFEFEF;
							border-radius: 8rpx;
							font-size: 18rpx;
							flex: 1;
							// 文字居中
							text-align: center;
							font-family: DeYiHei;

						}
					}

					.player-killing-list-item-right {
						// margin: 0 25rpx;
						// font-size: 35rpx;
						// // font-weight: bold;
						// color: black;
						// transform: rotate(-4.6deg);
						// -webkit-text-stroke: 2rpx white;

						// // border: 3.5rpx solid rgba(255, 255, 255, 1);
						// font-family: AlimamaShuHeiTi;

						margin: 0 25rpx;
						width: 100rpx;
					}
				}
			}
		}
	}
</style>