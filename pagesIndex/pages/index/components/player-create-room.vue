<template>
	<view class="container">
		<!-- 对战 -->
		<view class="player-killing">
			<view class="game-btn match-game" @click="handleMatchGameBtnClick">匹配对战</view>
			<view class="game-btn create-room" @click="handleCreateRoomBtnClick">创建房间</view>
		</view>
	</view>

</template>

<script setup lang="ts">
	const emit = defineEmits(["matchGameBtnClick", "createRoomBtnClick"]);
	function handleMatchGameBtnClick() {
		emit("matchGameBtnClick");
	}
	function handleCreateRoomBtnClick() {
		emit("createRoomBtnClick");
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;

		.player-killing {
			display: flex;
			gap: 10rpx;
			overflow: hidden;

			.game-btn {
				flex: 1;
				height: 122rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				color: black;
				font-family: AlimamaShuHeiTi;
			}

			.match-game {
				margin-left: -25px;
				border-image: url('@/static/images/icon_matchGame.png') 0 0 0 0 fill / 1px 0 stretch;
			}

			.create-room {
				margin-right: -25px;
				border-image: url('@/static/images/icon_createRoom.png') 0 0 0 0 fill / 1px 0 stretch;
			}

		}
	}
</style>