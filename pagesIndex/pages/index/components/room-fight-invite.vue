<template>
	<view class="container">
		<view class="room-fight-invite-container" @click="handleFightInviteClick">
			<view class="room-fight-invite">
				<text>对战邀请</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	const emit = defineEmits(["fightInviteClick"]);
	function handleFightInviteClick() {
		emit("fightInviteClick");
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;

		.room-fight-invite-container {
			background-color: $color-383838;
			margin-left: 25rpx;
			margin-right: 25rpx;
			border-radius: 50rpx 30rpx;
			display: flex;
			flex-direction: column;
			overflow: hidden;

			.room-fight-invite {
				margin: 10rpx 15rpx 20rpx 10rpx;
				border-radius: 50rpx 30rpx;
				// 设置背景图片
				background-image: url("@/static/images/icon_fightInviteBtn_bg.png");
				background-size: cover;
				text-align: center;
				padding: 20rpx 0;
				font-family: YouSheBiaoTiHei;

				text {
					font-size: 42rpx;
				}
			}
		}

	}
</style>