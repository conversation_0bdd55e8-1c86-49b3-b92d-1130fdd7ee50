<template>
	<view class="container">
		<!-- 对战 -->
		<view class="player-killing">
			<view class="game-btn match-game" @click="handleMatchGameBtnClick">匹配对战</view>
			<!-- <uv-gap :height="createRoomExpandGapHeight"></uv-gap> -->
			<uv-gap height="65rpx"></uv-gap>
			<view class="game-btn create-room" @click="handleCreateRoomBtnClick">创建房间</view>
		</view>
	</view>

</template>

<script setup lang="ts">
	// defineProps({
	// 	createRoomExpandGapHeight: {
	// 		type: String,
	// 		default: "0px"
	// 	}
	// })

	const emit = defineEmits(["matchGameBtnClick", "createRoomBtnClick"]);
	function handleMatchGameBtnClick() {
		emit("matchGameBtnClick");
	}
	function handleCreateRoomBtnClick() {
		emit("createRoomBtnClick");
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;

		.player-killing {
			display: flex;
			flex-direction: column;

			.game-btn {
				height: 55px;
				width: 700rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				color: black;
				font-family: AlimamaShuHeiTi;
			}

			.match-game {
				margin-left: -10rpx;
				// 实现点9图
				border-image: url('@/static/images/icon_matchGame.png') 0 100 0 100 fill / 1px 55px stretch;
			}

			.create-room {
				margin-left: 60rpx;
				border-image: url('@/static/images/icon_createRoom.png') 0 100 0 100 fill / 1px 55px stretch;
			}

		}
	}
</style>