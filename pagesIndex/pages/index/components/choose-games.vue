<template>
	<view class="container">
		<template v-if="isChooseGame">
			<view class="game-logo" @click="handleChangeGame">
				<image class="logo" :src="props.pageGameInfo.img" mode="aspectFit"></image>
			</view>
			<view class="game-mode" @click="handleChangeGame">
				<uv-button :customTextStyle="gameModeBtnTextStyle" :custom-style="gameModeBtnStyle" color="#fff"
					:text="props.pageGameInfo.gameTypeName"></uv-button>
			</view>

			<!-- <view style="padding: 50rpx 0 ; background-color: rgba(255, 255, 255, 1);">
				<uv-swiper bgColor="rgba(255, 255, 255, 1)" height="12vh" :list="list" previousMargin="180rpx"
					nextMargin="180rpx" :autoplay="false" radius="5" keyName="url" circular>
				</uv-swiper>
			</view>
			<view class="game-mode">
				<uv-button :customTextStyle="gameModeBtnTextStyle" :custom-style="gameModeBtnStyle" color="#fff"
					:text="props.pageGameInfo.gameTypeName"></uv-button>
			</view> -->
		</template>
	</view>
</template>

<script setup lang="ts">
	import { ref, reactive } from 'vue';

	import type { UserLikeVO } from '@/types/member.d.ts'
	// 获取 props
	const props = defineProps<{
		pageGameInfo : UserLikeVO
	}>()

	const gameModeBtnStyle = {
		height: '50rpx',
		color: 'black',
		borderRadius: '40rpx',//圆角
	};
	const gameModeBtnTextStyle = {
		fontSize: '25rpx',
	};
	let isChooseGame = ref(true)
	const list = reactive([{
		url: 'https://cdn.uviewui.com/uview/swiper/swiper1.png',
		title: '昨夜星辰昨夜风，画楼西畔桂堂东',
	}, {
		url: 'https://cdn.uviewui.com/uview/swiper/swiper2.png',
		title: '身无彩凤双飞翼，心有灵犀一点通',
	}, {
		url: 'https://cdn.uviewui.com/uview/swiper/swiper3.png',
		title: '谁念西风独自凉，萧萧黄叶闭疏窗，沉思往事立残阳'
	}]);
	const emit = defineEmits(['changeGame'])
	const handleChangeGame = () => {
		emit('changeGame')
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;

		.game-logo {
			width: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: rgba(253, 222, 64, 0.5);

			.logo {
				margin: 60rpx 0;
				height: 10vh;
			}

		}

		.game-mode {
			background-color: $color-383838;
			padding: 15rpx;
			font-family: DeYiHei;
		}
	}
</style>