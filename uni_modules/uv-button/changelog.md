## 1.0.15（2023-12-20）
1. 优化
## 1.0.14（2023-12-06）
1. 优化
## 1.0.13（2023-12-06）
1. 阻止事件冒泡处理
## 1.0.12（2023-10-19）
1. 增加后置插槽
## 1.0.11（2023-09-21）
1. 修复通过customStyle修改按钮宽度，组件中最外层节点不改变的问题
## 1.0.10（2023-09-15）
1. 按钮支持open-type="agreePrivacyAuthorization"
## 1.0.9（2023-09-11）
1. 增加参数iconSize，用于控制图标的大小
## 1.0.8（2023-09-10）
1. 修复多个按钮在一行宽度不正常的BUG
## 1.0.7（2023-09-07）
1. 修复warning颜色对应错误的BUG
## 1.0.6（2023-07-25）
1. 增加customTextStyle属性，方便自定义文字样式
## 1.0.5（2023-07-20）
1. 解决微信小程序动态设置hover-class点击态不消失的BUG
## 1.0.4（2023-06-29）
1. 修改上次更新出现nvue报错异常
## 1.0.3（2023-06-28）
 修复：设置open-type="chooseAvatar"等值不生效的BUG
## 1.0.2（2023-06-01）
1. 修复按钮点击触发两次的BUG
## 1.0.1（2023-05-16）
1. 优化组件依赖，修改后无需全局引入，组件导入即可使用
2. 优化部分功能
## 1.0.0（2023-05-10）
uv-button 按钮
