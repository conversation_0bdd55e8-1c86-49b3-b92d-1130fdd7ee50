## 1.0.9（2023-08-14）
1. 修复设置labelWidth属性时，节点渲染有闪动的BUG
## 1.0.8（2023-08-13）
1. 修复未设置rules的情况下报错的BUG
2. 优化错误提示
## 1.0.7（2023-08-10）
1. 修复在vue3+setup语法糖中错误文字动画错乱
## 1.0.6（2023-07-17）
1. 优化文档
2. 优化其他
## 1.0.5（2023-07-03）
去除插槽判断，避免某些平台不显示的BUG
## 1.0.4（2023-07-02）
uv-form  由于弹出层uv-transition的修改，组件内部做了相应的修改，参数不变。
## 1.0.3（2023-06-18）
1. 修改某些情况下的BUG
## 1.0.2（2023-06-15）
1.  修复支付宝报错的BUG
## 1.0.1（2023-05-16）
1. 优化组件依赖，修改后无需全局引入，组件导入即可使用
2. 优化部分功能
## 1.0.0（2023-05-10）
uv-form 表单
