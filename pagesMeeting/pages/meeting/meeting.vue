<template>
	<view class="container">
		<scroll-view scroll-x>


			<view class="scroll-view-container">
				<view class="live-container">
					<live-pusher class="live-pusher" :mode="push.mode" :autopush="push.autopush" :url="push.url"
						:enable-mic="push.enableMic" device-position="back" :enable-camera="push.enableCamera"
						:beauty="push.beautyLevel" :zoom="push.enableZoom" :orientation="push.videoOrientation"
						:video-width="push.videoWidth" :video-height="push.videoHeight" :min-bitrate="push.minBitrate"
						:max-bitrate="push.maxBitrate" @statechange="_pusherStateChangeHandler"
						@netstatus="_pusherNetStatusHandler" @error="_pusherErrorHandler"
						@bgmstart="_pusherBGMStartHandler" @bgmprogress="_pusherBGMProgressHandler"
						@bgmcomplete="_pusherBGMCompleteHandler" @audiovolumenotify="_pusherAudioVolumeNotify">
					</live-pusher>
				</view>
				<view class="live-player-container" v-for="item in playerArray" :key="item.streamID">
					<live-player class="live-player" :id="item.streamID" :data-userid="item.userID"
						:data-streamid="item.streamID" :data-streamtype="item.streamType" :src="item.src"
						:mode="item.mode" :autoplay="item.autoplay" :mute-audio="item.muteAudio"
						:mute-video="item.muteVideo" :orientation="item.orientation" :object-fit="item.objectFit"
						:background-mute="item.enableBackgroundMute" :min-cache="item.minCache"
						:max-cache="item.maxCache" :sound-mode="item.soundMode"
						:enable-recv-message="item.enableRecvMessage" :auto-pause-if-navigate="item.autoPauseIfNavigate"
						:auto-pause-if-open-native="item.autoPauseIfOpenNative" :debug="item.debug"
						@statechange="_playerStateChange" @fullscreenchange="_playerFullscreenChange"
						@netstatus="_playerNetStatus" @audiovolumenotify="_playerAudioVolumeNotify">
					</live-player>
				</view>
				<!-- <view class="live-container" v-for="item in playerArray" :key="item.streamID">
					<live-player class="live-player" :id="item.streamID" :data-userid="item.userID"
						:data-streamid="item.streamID" :data-streamtype="item.streamType" :src="item.src"
						:mode="item.mode" :autoplay="item.autoplay" :mute-audio="item.muteAudio"
						:mute-video="item.muteVideo" :orientation="item.orientation" :object-fit="item.objectFit"
						:background-mute="item.enableBackgroundMute" :min-cache="item.minCache"
						:max-cache="item.maxCache" :sound-mode="item.soundMode"
						:enable-recv-message="item.enableRecvMessage" :auto-pause-if-navigate="item.autoPauseIfNavigate"
						:auto-pause-if-open-native="item.autoPauseIfOpenNative" :debug="item.debug"
						@statechange="_playerStateChange" @fullscreenchange="_playerFullscreenChange"
						@netstatus="_playerNetStatus" @audiovolumenotify="_playerAudioVolumeNotify">
					</live-player>
				</view> -->
			</view>


		</scroll-view>
		<view class="tips-view-container">
			<view class="tips-view">
				<view class="tips-top">
					<text class="tips-top-top">建议将手机架起于牌垫右上方倾斜拍摄</text>
					<view class="tips-top-center">
						<view class="phone-tips">请将手机架起</view>
						<uv-gap height="8rpx"></uv-gap>
						<view class="controls">
							<!-- <view class="mic" @click="pusherAudioHandler">
								<image style="width: 30rpx; height: 30rpx;"
									:src="push.enableMic == true ? '/pagesMeeting/static/images/icon-mic-open-black.png' : '/pagesMeeting/static/images/icon-mic-close-black.png'"
									mode=""></image>
							</view> -->
							<view class="volume" @click="muteAllPlayerHandle('muteAudio')">
								<image style="width: 30rpx; height: 30rpx;"
									:src="isMuteAllPlayer == true ? '/pagesMeeting/static/images/icon-volume-close-black.png' : '/pagesMeeting/static/images/icon-volume-open-black.png'"
									mode=""></image>
							</view>
						</view>
					</view>
					<text class="tips-top-bottom">牌垫区域</text>

				</view>
				<uv-gap height="8rpx"></uv-gap>
				<view class="tips-bottom">
					<text class="tips-bottom-top">建议将角度调节至可看见玩家手牌的牌背</text>
					<text class="tips-bottom-bottom">玩家区域</text>
				</view>
			</view>
		</view>
		<view class="arrow-upward" style="position: absolute;"
			:style="{left : wxMenuCloseBtnCenter + 'px' ,top : wxMenuBottom + 'px'}">
			<uv-icon name="arrow-upward" color="black" size="20px"></uv-icon>
		</view>
		<view class="close-tips"
			style="position: absolute; padding: 10rpx 20rpx; display: flex; background-color: rgba(67, 207, 124, 1); border-radius: 26rpx; border: 1.74rpx solid black;"
			:style="{right : wxMenuRight + 'px' ,top : wxMenuBottom + 20 + 'px'}">
			<text style="font-size: 15rpx; font-weight: 400;">保留通话并</text>
			<text style="font-size: 15rpx; font-weight: 900;">退至</text>
			<text style="font-size: 15rpx; font-weight: 900; color: rgba(212, 48, 48, 1);">后台</text>
		</view>

		<view class="close" style="position: absolute; display: flex; align-items: center; justify-content: center;"
			:style="{right : wxMenuRight + 'px' ,top : tipsTopCenterRect.top + 'px', height : tipsTopCenterRect.height  + 'px'}">
			<view
				style="padding: 15rpx 52rpx; display: flex; background-color: rgba(212, 48, 48, 1); border-radius: 25rpx;"
				@click="onLeftClick">
				<text style="font-size: 17rpx; font-weight: 900; color: white;">结束通话</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import {
		onLoad,
		onUnload,
		onReady,
	} from "@dcloudio/uni-app";
	import { getCurrentInstance, ref } from 'vue';
	import type { MeetingDetail } from '@/types/room.d.ts';
	import { useMemberStore } from '@/stores/index';
	import TRTC from '@/pagesMeeting/lib/trtc-wx'
	import { leaveTVRoomAPI } from "@/services/room";
	const {
		ctx
	} : any = getCurrentInstance();
	let TRTCInstance = null;
	let push = ref({});
	let meetingDetail = ref<MeetingDetail>()
	let playerArray = ref([]);

	const onLeftClick = () => {
		uni.navigateBack()
	};
	// header起始位置
	const wxMenuBottom = ref(0)
	const wxMenuWidth = uni.getMenuButtonBoundingClientRect().width / 4
	const wxMenuCloseBtnCenter = ref(0)
	const wxMenuRight = ref(0)
	const tipsTopCenterRect = ref({});
	onLoad((options) => {
		const meetingInfo = JSON.parse(options!.meetingDetail)
		meetingDetail.value = meetingInfo;
		// uni.setNavigationBarTitle({
		// 	title: `房间号：${options.strRoomID}`,
		// });
		createEvent();
		console.log("wxMenuBottom在onLoad里面的值", wxMenuBottom.value);
	});
	onUnload(() => {
		removeEvent()
		exitRoom()
		leaveMeeting()
		// 保持屏幕常亮
		uni.setKeepScreenOn({
			keepScreenOn: true
		});
	});
	onReady(async () => {
		TRTCInstance = new TRTC();
		init()
		bindTRTCRoomEvent()
		enterRoom()
		// 延迟一秒
		setTimeout(async () => {
			wxMenuBottom.value = uni.getMenuButtonBoundingClientRect().bottom
			wxMenuCloseBtnCenter.value = uni.getMenuButtonBoundingClientRect().right - wxMenuWidth - 10;
			wxMenuRight.value = uni.getSystemInfoSync().windowWidth - uni.getMenuButtonBoundingClientRect().right;
			tipsTopCenterRect.value = await ctx.$uv.getRect('.tips-top-center');
			console.log("wxMenuBottom在onReady里面的值", wxMenuBottom.value);
		}, 1000);
	});
	// 创建事件监听
	const createEvent = () => {
		uni.$on('onTcgWebOwnerEndFighting', onTcgWebOwnerEndFighting)
		uni.$on('leaveTVRoom', leaveTVRoom)
	}

	// 移除事件监听
	const removeEvent = () => {
		uni.$off('onTcgWebOwnerEndFighting', onTcgWebOwnerEndFighting)
		uni.$off('leaveTVRoom', leaveTVRoom)
	}

	const leaveTVRoom = () => {
		uni.navigateBack();
	}

	const onTcgWebOwnerEndFighting = () => {
		uni.navigateBack();
	}

	const leaveMeeting = () => {
		leaveTVRoomAPI(meetingDetail.value.rid).then(() => {
			console.log('离开会议成功')
		}).catch(() => {
			console.log('离开会议失败')
		})
	};

	const init = () => {
		const value = TRTCInstance.createPusher({
			enableMic: meetingDetail.value.enableMic,
			enableCamera: meetingDetail.value.enableCamera,
			videoOrientation: 'vertical',
			videoWidth: 1280,
			videoHeight: 720,
			minBitrate: 600,
			maxBitrate: 1800,
			enableZoom: true,
		})
		push.value = { ...value }
	};
	const enterRoom = () => {
		// push.value = TRTCInstance.enterRoom({
		// 	userID: useMemberStore().profile.uid,
		// 	sdkAppID: meetingDetail.value.sdkAppID,
		// 	userSig: useMemberStore().profile.userSig,
		// 	strRoomID: meetingDetail.value.strRoomID,
		// 	scene: 'live',
		// 	enableMic: meetingDetail.value.enableMic,
		// 	enableCamera: meetingDetail.value.enableCamera,
		// 	videoOrientation: 'horizontal',
		// 	videoWidth: 1920,
		// 	videoHeight: 1080,
		// 	minBitrate: 600,
		// 	maxBitrate: 3000,
		// 	enableZoom: true,
		// })
		const value = TRTCInstance.enterRoom({
			userID: useMemberStore().profile.uid,
			sdkAppID: meetingDetail.value.sdkAppID,
			userSig: useMemberStore().profile.userSig,
			strRoomID: meetingDetail.value.strRoomID,
			scene: 'live',
		})
		push.value = { ...value }
		TRTCInstance.getPusherInstance().start() // 开始推流（autoPush的模式下不需要）
	};
	const exitRoom = () => {
		const {
			pusher,
			playerList,
		} = TRTCInstance.exitRoom()
		push.value = { ...pusher }
		playerArray.value = playerList
	};
	const bindTRTCRoomEvent = () => {
		const TRTC_EVENT = TRTCInstance.EVENT
		// 初始化事件订阅
		TRTCInstance.on(TRTC_EVENT.LOCAL_JOIN, (event : any) => {
			console.log('* room LOCAL_JOIN', event)
			uni.setKeepScreenOn({
				keepScreenOn: true
			});
		})
		TRTCInstance.on(TRTC_EVENT.LOCAL_LEAVE, (event : any) => {
			console.log('* room LOCAL_LEAVE', event)
			uni.setKeepScreenOn({
				keepScreenOn: true
			});
		})
		TRTCInstance.on(TRTC_EVENT.ERROR, (event : any) => {
			console.log('* room ERROR', event)
			uni.setKeepScreenOn({
				keepScreenOn: true
			});
		})
		TRTCInstance.on(TRTC_EVENT.REMOTE_USER_JOIN, (event : any) => {
			console.log('* room REMOTE_USER_JOIN', event)
			const {
				userID,
			} = event.data
			uni.setKeepScreenOn({
				keepScreenOn: true
			});
			// uni.showToast({
			// 	title: `${userID} 进入了房间`,
			// 	icon: 'none',
			// })
		})
		// 远端用户退出
		TRTCInstance.on(TRTC_EVENT.REMOTE_USER_LEAVE, (event : any) => {
			console.log('* room REMOTE_USER_LEAVE', event)
			const {
				userID,
				playerList,
			} = event.data
			playerArray.value = playerList
			// uni.showToast({
			// 	title: `${userID} 离开了房间`,
			// 	icon: 'none',
			// })
			// 打印userID的类型
			console.log('有人退出了,userID:', userID)
			// 判断userID字符串中是否包含PC字符
			if (!userID.includes('PC')) {
				uni.navigateBack()
			}
		})
		// 远端用户推送视频
		TRTCInstance.on(TRTC_EVENT.REMOTE_VIDEO_ADD, (event : any) => {
			console.log('* room REMOTE_VIDEO_ADD', event)
			const {
				player,
			} = event.data
			// 开始播放远端的视频流，默认是不播放的(设置为true表示不播放, 因为小程序端无须拉取视频流,所以不用播放视频流)
			setPlayerAttributesHandler(player, {
				muteVideo: true,
			})
			uni.setKeepScreenOn({
				keepScreenOn: true
			});
		})
		// 远端用户取消推送视频
		TRTCInstance.on(TRTC_EVENT.REMOTE_VIDEO_REMOVE, (event : any) => {
			console.log('* room REMOTE_VIDEO_REMOVE', event)
			const {
				player,
			} = event.data
			setPlayerAttributesHandler(player, {
				muteVideo: true,
			})
			uni.setKeepScreenOn({
				keepScreenOn: true
			});
		})
		// 远端用户推送音频
		TRTCInstance.on(TRTC_EVENT.REMOTE_AUDIO_ADD, (event : any) => {
			console.log('* room REMOTE_AUDIO_ADD', event)
			const {
				player,
			} = event.data
			setPlayerAttributesHandler(player, {
				muteAudio: isMuteAllPlayer.value,
			})
			uni.setKeepScreenOn({
				keepScreenOn: true
			});
		})
		// 远端用户取消推送音频
		TRTCInstance.on(TRTC_EVENT.REMOTE_AUDIO_REMOVE, (event : any) => {
			console.log('* room REMOTE_AUDIO_REMOVE', event)
			const {
				player,
			} = event.data
			setPlayerAttributesHandler(player, {
				muteAudio: true,
			})
			uni.setKeepScreenOn({
				keepScreenOn: true
			});
		})
		TRTCInstance.on(TRTC_EVENT.REMOTE_AUDIO_VOLUME_UPDATE, (event : any) => {
			console.log('* room REMOTE_AUDIO_VOLUME_UPDATE', event)
			const {
				playerList,
			} = event.data
			playerArray.value = playerList
			uni.setKeepScreenOn({
				keepScreenOn: true
			});
		})
		TRTCInstance.on(TRTC_EVENT.LOCAL_AUDIO_VOLUME_UPDATE, (event : any) => {
			// console.log('* room LOCAL_AUDIO_VOLUME_UPDATE', event)
			const {
				pusher,
			} = event.data

			push.value = { ...pusher }
			uni.setKeepScreenOn({
				keepScreenOn: true
			});
		})
		TRTCInstance.on(TRTC_EVENT.KICKED_OUT, () => {
			// console.log('* room LOCAL_AUDIO_VOLUME_UPDATE', event)
			uni.showToast({
				title: '房间已解散',
				icon: 'none',
			})
			// uni.navigateBack();
		})
	};
	// 页面点击事件
	// 开启/关闭推流的麦克风
	const pusherAudioHandler = () => {
		setPusherAttributesHandler({
			enableMic: !push.value.enableMic,
		})
	}
	const isMuteAllPlayer = ref(false)
	// 开启/关闭全部拉流的配置
	const muteAllPlayerHandle = (key) => {
		playerArray.value.forEach((player) => {
			mutePlayerHandle(key, player)
		})
		isMuteAllPlayer.value = !isMuteAllPlayer.value
		pusherAudioHandler()
	}


	const mutePlayerHandle = (key, player) => {
		setPlayerAttributesHandler(player, {
			[key]: !player[key],
		})
	}

	// 设置 pusher 属性
	const setPusherAttributesHandler = (options : any) => {
		const value = TRTCInstance.setPusherAttributes(options)
		push.value = { ...value };
	}
	// 设置某个 player 属性
	const setPlayerAttributesHandler = (player : any, options : any) => {
		playerArray.value = TRTCInstance.setPlayerAttributes(player.streamID, options)
	}


	// 请保持跟 wxml 中绑定的事件名称一致
	const _pusherStateChangeHandler = (event) => {
		TRTCInstance.pusherEventHandler(event)
	};
	const _pusherNetStatusHandler = (event) => {
		TRTCInstance.pusherNetStatusHandler(event)
	};

	const _pusherErrorHandler = (event) => {
		TRTCInstance.pusherErrorHandler(event)
	};

	const _pusherBGMStartHandler = (event) => {
		TRTCInstance.pusherBGMStartHandler(event)
	};

	const _pusherBGMProgressHandler = (event) => {
		TRTCInstance.pusherBGMProgressHandler(event)
	};

	const _pusherBGMCompleteHandler = (event) => {
		TRTCInstance.pusherBGMCompleteHandler(event)
	};

	const _pusherAudioVolumeNotify = (event) => {
		TRTCInstance.pusherAudioVolumeNotify(event)
	};

	const _playerStateChange = (event) => {
		TRTCInstance.playerEventHandler(event)
	};

	const _playerFullscreenChange = (event) => {
		TRTCInstance.playerFullscreenChange(event)
	};

	const _playerNetStatus = (event) => {
		TRTCInstance.playerNetStatus(event)
	};

	const _playerAudioVolumeNotify = (event) => {
		TRTCInstance.playerAudioVolumeNotify(event)
	};
</script>

<style lang="scss" scoped>
	@import '@/pagesMeeting/pages/meeting/meeting.scss'
</style>