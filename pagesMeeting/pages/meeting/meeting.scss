// $font-color: #ffffff;

.container {
  box-sizing: border-box;
  // background-image: url('https://mc.qcloudimg.com/static/img/7da57e0050d308e2e1b1e31afbc42929/bg.png');
  // 设置背景图片 平铺
  background-image: url("@/static/images/yellow_bg.png");
  background-repeat: repeat;
  width: 100%;
  height: 100vh;
  position: relative;
  
  .scroll-view-container {
  	display: grid;
  	grid-template-columns: repeat(auto-fill, 100vw);
	 // 改成每列只有一行,一行占比100vh
	grid-template-rows: 100vh;
  	// grid-template-rows: 50vh 50vh;
  	grid-auto-flow: column;
  	
  	.live-container {
  		width: 100vw;
  	}
  }
  .tips-view-container {
	  position: absolute;
	  left: 0;
	  top: 0;
	  bottom: 0;
	  right: 0;
	  display: flex;
	  .tips-view {
		  flex: 1;
		  margin: 10rpx 50rpx 10rpx 50rpx;
		  display: flex;
		  flex-direction: column;
		  .tips-top {
			  flex: 4;
			  width: 100%;
			  border-radius: 19rpx;
			  border: 1px dashed rgba(0, 0, 0, 0.42);
			  display: flex;
			  align-items: center;
			  justify-content: center;
			  position: relative;
			  .tips-top-top {
				  position: absolute;
				  right: 19rpx;
				  top: 10rpx;
				  color: rgba(66, 66, 66, 1);
				  font-size: 15rpx;
			  }
			  .tips-top-center {
				  display: flex;
				  flex-direction: column;
				  .phone-tips {
					  height: 54rpx;
					  width: 133rpx;
					  line-height: 54rpx;
					  border-radius: 19rpx;
					  background-color: rgba(229, 229, 229, 0.79);
					  text-align: center;
					  color: rgba(66, 66, 66, 1);
					  font-size: 15rpx;
				  }
				  .controls {
					   display: flex;
					   justify-content: space-around;
					  .mic {
						  width: 40rpx;
						  height: 40rpx;
						  border-radius: 50%;
						  background-color: rgba(229, 229, 229, 0.79);
						  display: flex;
						  justify-content: center;
						  align-items: center;
					  }
					  .volume {
						  width: 40rpx;
					  	  height: 40rpx;
						  border-radius: 50%;
					  	  background-color: rgba(229, 229, 229, 0.79);
						  display: flex;
						  justify-content: center;
						  align-items: center;
					  }
				  }
			  }
			  .tips-top-bottom {
				  position: absolute;
			  	  left: 19rpx;
			  	  bottom: 10rpx;
			  	  color: rgba(66, 66, 66, 1);
				  font-size: 15rpx;
				  
			  }
		  }
		  .tips-bottom {
			  flex: 1;
			  width: 100%;
			  border-radius: 19rpx;
			  border: 1px dashed rgba(0, 0, 0, 0.42);
			  position: relative;
			  .tips-bottom-top {
				  position: absolute;
				  right: 19rpx;
				  top: 10rpx;
				  color: rgba(66, 66, 66, 1);
				  font-size: 15rpx;
			  }
			  
			  .tips-bottom-bottom {
				  position: absolute;
				  left: 19rpx;
				  bottom: 10rpx;
				  color: rgba(66, 66, 66, 1);
				  font-size: 15rpx;
			  }
		  }
	  }
  }
}



live-pusher.live-pusher {
  height: 100%;
  width: 100%;
}

live-player.live-player {
  height: 100%;
  width: 100%;
}

	
