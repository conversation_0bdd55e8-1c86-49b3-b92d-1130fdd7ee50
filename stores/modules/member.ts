import { defineStore } from 'pinia'
import { reactive } from 'vue'
import { LoginResult } from '@/types/member.d.ts'



// 定义 Store
export const useMemberStore = defineStore(
	'member', () => {
		// 会员信息
		const profile : LoginResult = reactive<LoginResult>({})

		// 保存会员信息，登录时使用
		const setProfile = (val : LoginResult) => {
			Object.assign(profile, val);
			uni.$emit('loginSuccess')

		}

		// 清理会员信息，退出时使用
		const clearProfile = () => {
			Object.keys(profile).forEach(key => {
				profile[key] = undefined;
			});
			uni.$emit('logoutSuccess')
		}

		// 记得 return
		return {
			profile,
			setProfile,
			clearProfile,
		}
	},
	// TODO: 持久化
	{
		persist: {
			storage: {
				getItem(key) {
					return uni.getStorageSync(key)
				},
				setItem(key, value) {
					uni.setStorageSync(key, value)
				},
			},
		},
	},
)