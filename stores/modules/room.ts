import { defineStore } from 'pinia'
import { reactive } from 'vue'
import { RoomDetail } from '@/types/room.d.ts'
export const useRoomStore = defineStore('room', () => {
	// 房间信息
	const roomDetail : RoomDetail = reactive<RoomDetail>({})

	// 保存房间信息
	const setRoomDetail = (val : RoomDetail) => {
		Object.assign(roomDetail, val);
	}

	// 清理房间信息
	const clearRoomDetail = () => {
		Object.keys(roomDetail).forEach(key => {
			roomDetail[key] = undefined;
		});
	}

	// 记得 return
	return {
		roomDetail,
		setRoomDetail,
		clearRoomDetail,
	}
})

// RoomDetail