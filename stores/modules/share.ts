import { defineStore } from 'pinia'
import { reactive } from 'vue'
import { ShareParams } from '@/types/share.d.ts'
export const useShareStore = defineStore('share', () => {

	// 分享的参数
	const shareParams : ShareParams = reactive<ShareParams>({})

	// 保存分享参数
	const setShareParams = (val : ShareParams) => {
		Object.assign(shareParams, val);
	}

	// 清理分享参数
	const clearShareParams = () => {
		Object.keys(shareParams).forEach(key => {
			shareParams[key as keyof ShareParams] = undefined;
		});
	}


	return { shareParams, setShareParams, clearShareParams }
})