import { defineStore } from 'pinia'
import { reactive } from 'vue'
import { GameDetail } from '@/types/game.d.ts'


// 定义 Store
export const useGamesStore = defineStore(
	'games', () => {
		// 游戏列表
		const gameList = reactive<GameDetail[]>([])
		// 保存游戏列表
		const setGameList = (val : GameDetail[]) => {
			gameList.length = 0;
			gameList.push(...val);
		}

		// 记得 return
		return {
			gameList,
			setGameList,
		}
	},
	// TODO: 持久化
	{
		persist: {
			storage: {
				getItem(key) {
					return uni.getStorageSync(key)
				},
				setItem(key, value) {
					uni.setStorageSync(key, value)
				},
			},
		},
	},
)