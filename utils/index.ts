

/**
 * 方法说明
 * author: 常超群
 * @description:  根据指定宽度(rpx)分割一个字符串为字符串数组
 * @param 
 * @return 
 * @createTime: 2023-12-03 21:30:10
 */
export function splitStringByPixelWidth(
	str : string,
	widthRpx : number,
	fontSizeRpx : number,
	fontFace : string
) : string[] {
	// 获取屏幕宽度
	const { screenWidth } = uni.getSystemInfoSync();
	const rpxToPx : number = screenWidth / 750; // rpx到px的转换率

	// 将rpx转换为px
	const width : number = widthRpx * rpxToPx;
	const fontSize : number = fontSizeRpx * rpxToPx;

	// 设置context的字体样式
	const context : UniApp.CanvasContext = uni.createCanvasContext('canvas');
	context.font = `${fontSize}px ${fontFace}`;
	let result : string[] = []; // 存储结果的数组
	let line : string = ''; // 当前行的文本
	// 遍历每个字符
	for (let i = 0; i < str.length; i++) {
		// 尝试添加下一个字符
		let testLine : string = line + str[i];
		// 计算测试行的宽度
		let testWidth : number = context.measureText(testLine).width;
		// 如果测试行的宽度大于指定的宽度，则添加当前行到结果数组，并开始新的一行
		if (testWidth > width && line !== '') {
			result.push(line);
			line = str[i];
		} else {
			// 否则，将测试行作为当前行
			line = testLine;
		}
	}
	// 将最后一行添加到结果数组（如果有内容）
	if (line !== '') {
		result.push(line);
	}

	return result;
}

// rpx转px
export const tcgRpxToPx = (rpx : number) => {
	const screenWidth = uni.getSystemInfoSync().screenWidth
	return (screenWidth * rpx) / 750
}
// px转rpx
export const tcgPxToRpx = (px : number) => {
	const screenWidth = uni.getSystemInfoSync().screenWidth
	return (750 * px) / screenWidth
}