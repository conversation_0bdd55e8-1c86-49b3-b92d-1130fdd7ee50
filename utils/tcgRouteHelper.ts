import { useRoomStore } from '@/stores/modules/room';
import { createRoomAPI, getRoomDetailAPI, getUserOnlineRoomAPI } from '@/services/room';
import { page, pages } from '@/uni_modules/uv-ui-tools/libs/function/index.js';
import { empty } from '@/uni_modules/uv-ui-tools/libs/function/test.js';
import route from '@/uni_modules/uv-ui-tools/libs/util/route.js';
import { RoomDetail } from '@/types/room.d.ts';
import { options as txOption } from '@/utils/TcgIMHelper'
import { useWatchRoomStore } from '@/stores/modules/watchRoom'
import { useMemberStore } from '@/stores/index';

/**
 * 方法说明
 * author: 常超群
 * @description: 收到官网创建房间的IM消息
 * @param 
 * @return 
 * @createTime: 2024-04-01 22:26:55
 */
export const onTcgWebCreateRoom = (rid : string) => {
	// 如何当前在房间页面,那么直接刷新房间信息
	if (page().includes("pagesRoom/room/room")) {
		getRoomDetailAPI(rid).then((res) => {
			let roomDetail = encodeURIComponent(JSON.stringify(res.data));
			route({
				url: '/pagesRoom/room/room',
				type: 'redirectTo',
				params: {
					roomDetail: roomDetail
				}
			})
		})
		return
	}
	// 如果不在房间页面,判断页面数组里面有没有房间页面,有的话先返回到房间页面,然后刷新房间信息
	let isInRoomPage : boolean = false
	const pageArray = pages();
	for (let i = 0; i < pageArray.length; i++) {
		const page = pageArray[i];
		if (page.route == "pagesRoom/room/room") {
			isInRoomPage = true
			const delta = pageArray.length - i - 1;
			uni.navigateBack({
				delta: delta,
				success: () => {
					getRoomDetailAPI(rid).then((res) => {
						let roomDetail = encodeURIComponent(JSON.stringify(res.data));
						route({
							url: '/pagesRoom/room/room',
							type: 'redirectTo',
							params: {
								roomDetail: roomDetail
							}
						})
					})
				}
			})
		}
	}

	// 如果页面数组里面没有房间页面,直接跳转到房间页面
	if (!isInRoomPage) {
		getRoomDetailAPI(rid).then((res) => {
			let roomDetail = encodeURIComponent(JSON.stringify(res.data));
			route({
				url: '/pagesRoom/room/room',
				params: {
					roomDetail: roomDetail
				}
			})
		})
	}

}

/**
 * 方法说明
 * author: 常超群
 * @description: 收到官网房主退出房间的IM消息
 * @param 
 * @return 
 * @createTime: 2024-04-01 22:26:55
 */
export const onTcgWebOwnerOutRoom = (rid : string) => {
	const pageArray = pages();
	for (let i = 0; i < pageArray.length; i++) {
		const page = pageArray[i];
		if (page.route == "pagesRoom/room/room") {
			const roomStore = useRoomStore()
			roomStore.clearRoomDetail();
			const delta = pageArray.length - i;
			uni.navigateBack({
				delta: delta
			})
		}
	}
}
/**
 * 方法说明
 * author: 常超群
 * @description: 收到官网玩家退出房间的IM消息
 * @param
 * @return
 * @createTime: 2024-04-01 22:26:55
 */
// export const onTcgWebPlayerOutRoom = (rid : string) => {
// 	console.log("收到官网玩家退出房间的IM消息");
// 	getRoomDetailAPI(rid).then((res) => {

// 		const pageArray = pages();
// 		for (let i = 0; i < pageArray.length; i++) {
// 			const page = pageArray[i];
// 			if (page.route == "pagesRoom/room/room") {
// 				const roomStore = useRoomStore()
// 				roomStore.clearRoomDetail();
// 				const delta = pageArray.length - i + 1;
// 				uni.navigateBack({
// 					delta: delta,
// 					success: () => {
// 						let roomDetail = JSON.stringify(res.data);
// 						route({
// 							url: '/pagesRoom/room/room',
// 							type: 'redirectTo',
// 							params: {
// 								roomDetail: roomDetail
// 							}
// 						})
// 					}
// 				})
// 			}
// 		}
// 	})
// }

/**
 * 方法说明
 * author: 常超群
 * @description: 小程序从后台返回到前台调用的方法
 * @createTime: 2024-04-03 19:53:27
 */

export const tcgAppOnShowRoute = () => {
	getUserOnlineRoomAPI().then(res => {
		compareRoomData(res.data)
	})
}
// 写一个方法, 用法是为了判断从后端返回的数据跟本地缓存数据做对比
const compareRoomData = (roomDetail : RoomDetail) => {
	const roomStore = useRoomStore()
	const watchRoomStore = useWatchRoomStore();
	const memberStore = useMemberStore();
	// 如果获取的后端数据为空,直接设置不显示观战弹窗
	if (empty(roomDetail)) {
		watchRoomStore.setIsShowWatchLive(false)
	}
	if (!empty(roomDetail)) {
		let user = roomDetail.userList.find(user => user.uid == memberStore.profile.uid);
		if (!empty(user)) {
			if (user.userType == 2) {
				watchRoomStore.setIsShowWatchLive(true)
				roomStore.clearRoomDetail();
				uni.navigateBack({
					delta: 100
				})
				return
			}
		}
	}

	// 如果获取的后端数据为空,但是缓存里不为空,那么说明房间已经解散,清除缓存,返回首页
	if (empty(roomDetail) && !empty(roomStore.roomDetail.rid)) {
		roomStore.clearRoomDetail();
		uni.showToast({
			title: '房间已解散',
			icon: 'none'
		})
		uni.navigateBack({
			delta: 100
		})
		return;
	}
	// 如果获取的后端数据不为空,但是缓存里为空,那么说明是新创建的房间,直接跳转到房间页面
	if (!empty(roomDetail) && empty(roomStore.roomDetail.rid)) {
		if (roomDetail.status == 1 || roomDetail.status == 2 || roomDetail.status == 3) {
			let roomInfo = encodeURIComponent(JSON.stringify(roomDetail));
			route({
				url: '/pagesRoom/room/room',
				params: {
					roomDetail: roomInfo
				}
			})
		}
		return;
	}
	// 如果获取的后端数据不为空,缓存里也不为空,那么判断是否是同一个房间
	if (!empty(roomDetail) && !empty(roomStore.roomDetail.rid) && roomDetail?.rid == roomStore.roomDetail.rid) {
		let isInMeetingPage : boolean = false
		let delta = 1
		const pageArray = pages();
		for (let i = 0; i < pageArray.length; i++) {
			const page = pageArray[i];
			if (page.route == "pagesMeeting/pages/meeting/meeting") {
				isInMeetingPage = true
				delta = pageArray.length - i;
			}
		}

		// 判断是否在游戏中.如果在游戏中,并且当前页面也是在游戏中页面,那么不做处理
		if (roomDetail.status == 3 && isInMeetingPage) {
			return
		}
		// 判断是否在游戏中.如果在游戏中,并且当前页面不是在游戏中页面的话.跳转到游戏中页面
		if (roomDetail.status == 3 && !isInMeetingPage) {
			let meetingDetail = JSON.stringify({
				rid: roomDetail.rid, // 直接在这里填入房间号
				sdkAppID: txOption.SDKAppID, // 直接填入sdkAppID
				strRoomID: roomDetail.tvGroupId, // 直接填入会议室号
				enableCamera: true, // 设置是否开启摄像头
				enableMic: true, // 设置是否开启话筒
			});
			route({
				url: '/pagesMeeting/pages/meeting/meeting',
				params: {
					meetingDetail: meetingDetail
				}
			})
			return
		}
		// 如果不在游戏中,并且当前页面在游戏中页面.那么直接返回
		if (roomDetail.status != 3 && isInMeetingPage) {
			uni.navigateBack({
				delta: delta
			})
			return
		}
		return
	}
	// 如果获取的后端数据不为空,缓存里也不为空,但不是同一个房间,那么返回到房间页面,且redirectTo当前房间页面
	if (!empty(roomDetail) && !empty(roomStore.roomDetail.rid) && roomDetail?.rid != roomStore.roomDetail.rid) {
		const pageArray = pages();
		for (let i = 0; i < pageArray.length; i++) {
			const page = pageArray[i];
			if (page.route == "pagesRoom/room/room") {
				const delta = pageArray.length - i - 1;
				uni.navigateBack({
					delta: delta,
					success: () => {
						let roomInfo = encodeURIComponent(JSON.stringify(roomDetail));
						route({
							url: '/pagesRoom/room/room',
							type: 'redirectTo',
							params: {
								roomDetail: roomInfo
							}
						})
					}
				})
			}
		}
	}
}