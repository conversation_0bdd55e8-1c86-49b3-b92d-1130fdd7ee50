import { TcgHttpError, ErrorResultData } from "@/utils/http";

// 建议循环调用方法，而不是这个方法内循环下载
// 下载字体文件，注意要把字体域名加到后台downloadFile白名单中
function _downloadFont(fontUrl : string, filePath : string, fontFamily : string) {

	return new Promise<string>((resolve, reject) => {

		uni.downloadFile({
			url: fontUrl,
			success: (downloadRes) => {
				if (downloadRes.statusCode == 200) {
					console.log('字体downloadFile下载成功', downloadRes)
					uni.getFileSystemManager().saveFile({ // 下载成功后保存到本地
						tempFilePath: downloadRes.tempFilePath,
						filePath,
						success: (saveFileRes) => {
							// 加载字体
							_loadFontFace(fontFamily, saveFileRes.savedFilePath).then(() => {
								resolve('ok');
							}).catch((error) => {
								reject(error);
							})
						},
						fail: (error) => {
							console.log('字体saveFile加载失败')
							let errorResultData : ErrorResultData = {
								msg: error.errMsg,
								url: `saveFile${fontFamily}`,
								code: 500
							}
							reject(new TcgHttpError(errorResultData));
						}
					})
				} else {
					console.log('字体downloadFile加载失败')
					let errorResultData : ErrorResultData = {
						msg: '字体downloadFile加载失败',
						url: `downloadFile${fontFamily}`,
						code: downloadRes.statusCode
					}
					reject(new TcgHttpError(errorResultData));
				}
			},
			fail: (error) => {
				console.log('字体downloadFile加载失败')
				let errorResultData : ErrorResultData = {
					msg: error.errMsg,
					url: `downloadFile${fontFamily}`,
					code: 500
				}
				reject(new TcgHttpError(errorResultData));
			},
		})
	});
}
// 加载文件字体转 base64，load
function _loadFontFace(fontFamily : string, filePath : string) {

	return new Promise<string>((resolve, reject) => {
		uni.getFileSystemManager().readFile({
			filePath, // 本地文件地址
			encoding: 'base64',
			success: (res) => {
				uni.loadFontFace({
					global: true, // 是否全局生效
					scopes: ['webview'], //native可能有点问题，超哥生个海报试一下
					family: fontFamily, // 字体名称
					source: `url("data:font/ttf;charset=utf-8;base64,${res.data}")`,
					success: (res) => {
						resolve('ok');
					},
					fail: (error) => {
						console.log('字体loadFontFace加载失败')
						let errorResultData : ErrorResultData = {
							msg: error.errMsg,
							url: `loadFontFace${fontFamily}`,
							code: 500
						}
						reject(new TcgHttpError(errorResultData));
					},
				})
			},
			fail: (error) => {
				console.log('字体readFile加载失败')
				let errorResultData : ErrorResultData = {
					msg: error.errMsg,
					url: `readFile${fontFamily}`,
					code: 500
				}
				reject(new TcgHttpError(errorResultData));
			},
		})
	});
}
// fontUrl: 字体地址
// filename: 存储文件路径
// fontFamily: css 中字体的 family
function loadCloudFontFace(fontUrl : string, fileName : string, fontFamily : string) {
	const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`
	return new Promise<string>((resolve, reject) => {
		uni.getFileSystemManager().access({
			path: filePath,
			success: () => {
				console.log('从本地加载了字体');
				_loadFontFace(fontFamily, filePath).then(() => {
					resolve('ok');
				}).catch((error) => {
					reject(error);
				})
			},
			fail: () => {
				console.log('从外部加载了字体', fontUrl);
				_downloadFont(fontUrl, filePath, fontFamily).then(() => {
					resolve('ok');
				}).catch((error) => {
					reject(error);
				})
			}
		})
	});
}

export default loadCloudFontFace