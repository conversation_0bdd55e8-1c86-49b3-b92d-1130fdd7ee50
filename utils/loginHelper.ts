import { useMemberStore } from '@/stores/index'
import { postLoginWxMinAPI, wxLogin, loginIM } from '@/services/login';
import { empty } from '@/uni_modules/uv-ui-tools/libs/function/test.js';
/**
 * 方法说明
 * author: 常超群
 * description: 是否登录
 * @param  无
 * @return  是否登录
 * @createTime: 2023-12-03 10:58:02
 */
export const isLogin = () : boolean => {
	const memberStore = useMemberStore()
	const token = memberStore.profile.accessToken
	if (empty(token)) {
		return false
	}
	return true
}

/**
 * 方法说明
 * author: 常超群
 * description:  登录到tcg
 * @param 
 * @return 
 * @createTime: 2023-12-03 12:39:29
 */
type loginTcgType = {
	loginCode : string;
	phoneCode ?: string;
}
export const loginTcg = async (data : loginTcgType, isShowLoading : boolean = false) => {
	if (isShowLoading) {
		uni.showLoading({
			title: ''
		})
	}
	try {
		const res = await postLoginWxMinAPI({
			loginCode: data.loginCode,
			phoneCode: data.phoneCode
		});
		const chatRes = await loginIM({
			userID: res.data.uid!,
			userSig: res.data.userSig!
		});

		// 保存会员信息
		const memberStore = useMemberStore();
		memberStore.setProfile(res.data);
		console.log('登录chat返回的信息', chatRes);
		if (isShowLoading) {
			uni.hideLoading();
		}
		return res
	} catch (e) {
		if (isShowLoading) {
			uni.hideLoading();
		}
		return Promise.reject(e)
	}
}
/**
 * 方法说明
 * author: 常超群
 * @description:  点击头像进行登录
 * @param 
 * @return 
 * @createTime: 2023-12-03 20:47:33
 */

export const loginTcgWithLoginCode = async (isShowLoading : boolean = false) => {
	if (isShowLoading) {
		uni.showLoading({
			title: ''
		})
	}
	try {
		const loginCode = await wxLogin()
		const res = await postLoginWxMinAPI({ loginCode });
		const chatRes = await loginIM({ userID: res.data.uid!, userSig: res.data.userSig! });

		// 保存会员信息
		const memberStore = useMemberStore();
		memberStore.setProfile(res.data);
		console.log('登录chat返回的信息', chatRes);
		if (isShowLoading) {
			uni.hideLoading();
		}
		return res
	} catch (e) {
		if (isShowLoading) {
			uni.hideLoading();
		}
		return Promise.reject(e)
	}
}