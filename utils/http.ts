import Request, { HttpRequestConfig } from '@/uni_modules/uv-ui-tools/libs/luch-request';
import { useMemberStore } from '@/stores/index'
import { isLogin, loginTcgWithLoginCode } from '@/utils/loginHelper'
import { page, pages } from '@/uni_modules/uv-ui-tools/libs/function/index.js';

// 请求队列
let requestList : any[] = []
// 是否正在刷新中
let isRefreshToken = false

// * 请求响应参数(包含data)
export type ResultData<T> = {
	code : number
	msg : string
	data : T
}
// * 请求响应参数分页的数据
export type ResultPaginationData<T> = {
	code : number
	msg : string
	rows : T
	total : number
}

// * 请求响应失败参数
export type ErrorResultData = {
	url ?: string
	msg ?: string
	code ?: number
}

export class TcgHttpError extends Error {
	data : ErrorResultData;

	constructor(data : ErrorResultData) {
		super(data.msg);
		this.data = data;
	}
}


const service = new Request()
// 全局配置
service.setConfig((config) => {
	/* 超时时间 */
	if (config.method == 'DOWNLOAD') {
		config.timeout = 60000
	} else {
		config.timeout = 10000
	}
	/* 根域名 */
	if (process.env.NODE_ENV === 'development') {
		config.baseURL = "https://www.duelchannel.com/min-api/tcg"
	} else {
		config.baseURL = "https://www.duelchannel.com/min-api/tcg"
	}
	return config
})
// 请求拦截器
service.interceptors.request.use(
	(config) => {
		// 初始化请求拦截器时，会执行此方法，此时data为undefined，赋予默认{}
		config.data = config.data || {}
		// 添加小程序端请求头标识
		config.header = {
			'tenantId': "000000",
			'clientId': 'b4e4aed95575c42e7d3b671b93c704a6',
			...config.header,
		}
		// 添加 token 请求头标识
		const memberStore = useMemberStore()
		const token = memberStore.profile.accessToken
		if (token) {
			config.header.Authorization = `Bearer ${token}`
		}
		console.log("请求的数据", config);
		return config
	},
	(config) => {
		let errorResultData : ErrorResultData = {
			msg: '请求失败',
			url: config.url,
			code: 500
		}
		return Promise.reject(new TcgHttpError(errorResultData))
	}
)
// 响应拦截器
service.interceptors.response.use(
	async (response) => {
		console.log("请求成功的数据", response);
		/*  对响应成功做点什么 （statusCode === 200）*/
		const { data } = response
		const config = response.config;
		if (data.code === 200) {
			const obj = {
				msg: '',
				isBanned: false
			}
			uni.$emit("onAccountIsBanned", obj)
			return data
		}
		if (data.code === 9527) {
			// 如果被封禁且不是loading页面
			if (!page().includes("pages/loading/loading")) {
				const obj = {
					msg: data.msg,
					isBanned: true
				}
				uni.$emit("onAccountIsBanned", obj)
			}
			uni.hideLoading()
			// 如果已经被封禁
			let errorResultData : ErrorResultData = {
				msg: data.msg,
				url: config.url,
				code: data.code
			}
			return Promise.reject(new TcgHttpError(errorResultData))
		} else {
			const obj = {
				msg: '',
				isBanned: false
			}
			uni.$emit("onAccountIsBanned", obj)
		}
		// * token过期 重新登录
		if (data.code === 401) {
			if (!isLogin()) {
				let errorResultData : ErrorResultData = {
					msg: data.msg,
					url: config.url,
					code: data.code
				}
				return Promise.reject(new TcgHttpError(errorResultData))
			}

			if (!isRefreshToken) {
				isRefreshToken = true
				// 1. 进行登录
				try {
					await loginTcgWithLoginCode()
					// 2.1 登录成功，则回放队列的请求 + 当前请求
					config.header.Authorization = `Bearer ${useMemberStore().profile.accessToken}`
					requestList.forEach((cb : any) => {
						cb()
					})
					requestList = []
					return service.request(config)
				} catch (e) {
					// 为什么需要 catch 异常呢？刷新失败时，请求因为 Promise.reject 触发异常。
					// 2.2 刷新失败，只回放队列的请求
					requestList.forEach((cb : any) => {
						cb()
					})
					// 提示是否要登出。即不回放当前请求！不然会形成递归
					let errorResultData : ErrorResultData = {
						msg: data.msg,
						url: config.url,
						code: data.code
					}
					return Promise.reject(new TcgHttpError(errorResultData))
				} finally {
					requestList = []
					isRefreshToken = false
				}
			} else {
				// 添加到队列，等待刷新获取到新的令牌
				return new Promise((resolve) => {
					requestList.push(() => {
						config.header.Authorization = `Bearer ${useMemberStore().profile.accessToken}`
						// 让每个请求携带自定义token 请根据实际情况自行修改
						resolve(service.request(config))
					})
				})
			}
		}
		uni.showToast({
			title: data.msg,
			icon: 'none'
		})
		let errorResultData : ErrorResultData = {
			msg: data.msg,
			url: config.url,
			code: data.code
		}
		return Promise.reject(new TcgHttpError(errorResultData))
	},
	(response) => {
		console.log("请求失败的数据", response);
		/*  对响应错误做点什么 （statusCode !== 200）*/
		const status = response?.statusCode
		// 处理 HTTP 网络错误
		let message = ''
		switch (status) {
			case 401:
				message = 'token 失效，请重新登录'
				break
			case 403:
				message = '拒绝访问'
				break
			case 404:
				message = '请求地址错误'
				break
			case 500:
				message = '服务器故障'
				break
			default:
				message = '网络连接故障'
		}
		uni.showToast({
			title: message,
			icon: 'none'
		})
		let errorResultData : ErrorResultData = {
			msg: message,
			url: response.config.url,
			code: status
		}
		return Promise.reject(new TcgHttpError(errorResultData))
	}
)

const http = {

	get<T>(url : string, params ?: object, config ?: HttpRequestConfig) : Promise<ResultData<T>> {
		return service.get(url, { params, ...config })
	},

	getPaginationList<T>(url : string, params ?: object, config ?: HttpRequestConfig) : Promise<ResultPaginationData<T>> {
		return service.get(url, { params, ...config })
	},

	post<T>(url : string, data ?: object, config ?: HttpRequestConfig) : Promise<ResultData<T>> {
		return service.post(url, data, config)
	},

	put<T>(url : string, data ?: object, config ?: HttpRequestConfig) : Promise<ResultData<T>> {
		return service.put(url, data, config)
	},

	delete<T>(url : string, data ?: object, config ?: HttpRequestConfig) : Promise<ResultData<T>> {
		return service.delete(url, data, config)
	},
	// *上传文件, 一般params不用传,所以放在第三个参数
	upload<T>(url : string, config ?: HttpRequestConfig) : Promise<ResultData<T>> {
		return service.upload(url, config)
	}
}
export default http