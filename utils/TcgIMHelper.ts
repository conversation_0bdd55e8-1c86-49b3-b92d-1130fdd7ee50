import TencentCloudChat, { Message } from '@tencentcloud/chat';
import TIMUploadPlugin from 'tim-upload-plugin';
import { loginTcgWithLoginCode } from '@/utils/loginHelper';
import { getMessageListAPI } from '@/services/message'
import { useMessageStore } from '@/stores/modules/message'
import { empty } from '@/uni_modules/uv-ui-tools/libs/function/test.js';
import { onTcgWebCreateRoom, onTcgWebOwnerOutRoom } from '@/utils/tcgRouteHelper';
// import route from '@/uni_modules/uv-ui-tools/libs/util/route.js';
// import { page } from '@/uni_modules/uv-ui-tools/libs/function/index.js';

export let options = {
	SDKAppID: 1600022351 // 接入时需要将0替换为您的即时通信 IM 应用的 SDKAppID
};
// 创建 SDK 实例，`TIM.create()`方法对于同一个 `SDKAppID` 只会返回同一份实例
let tcgChat = TencentCloudChat.create(options); // SDK 实例通常用 chat 表示

tcgChat.setLogLevel(0); // 普通级别，日志量较多，接入时建议使用
// chat.setLogLevel(1); // release 级别，SDK 输出关键信息，生产环境时建议使用
// 注册腾讯云即时通信 IM 上传插件
tcgChat.registerPlugin({ 'tim-upload-plugin': TIMUploadPlugin });

// 监听SDK可以使用的事件
let onSdkReady = function (event : any) {
	console.log("tcgIM监听可以使用啦");
};
tcgChat.on(TencentCloudChat.EVENT.SDK_READY, onSdkReady);
// 监听sdk不可以使用的事件
let onSdkNotReady = function (event : any) {
	console.log("tcgIM监听不可用");
};
tcgChat.on(TencentCloudChat.EVENT.SDK_NOT_READY, onSdkNotReady);

// 处理收到的自定义消息
const onCustomMessageReceived = async function (message : Message) {
	const data = JSON.parse(message.payload.data);
	switch (data.type) {
		case 20:
			// 20 - 邀请加入房间
			uni.$emit('inviteRoomListUpdate')
			break;
		case 10:
		case 30:
		case 31:
			// 10和30、31都是站内信列表需要更新 10好友申请 30站内信,31系统站内信
			const res = await getMessageListAPI({ status: 1 });
			useMessageStore().setUnreadMessageNum(res.data.total);
			uni.$emit('updateMessageList')
			break;
		case 40:
			// 40 - 收到点赞信息
			uni.showToast({
				icon: 'none',
				title: `您收到来自:${data.data.formNickName}的点赞`
			})
			break;
		case 100:
			// 100 - 收到创建房间消息
			console.log("收到创建房间消息")
			if (data.platform == "tcgmini") {
				break
			}
			onTcgWebCreateRoom(data.data.rid)
			break;
		case 101:
			// 101 - 收到加入房间消息
			console.log("收到加入房间消息")
			if (data.platform == "tcgmini") {
				break
			}
			onTcgWebCreateRoom(data.data.rid)
			break;
		case 102:
			// 102 - 收到解散房间[房主]消息
			console.log("收到解散房间[房主]消息")
			if (data.platform == "tcgmini") {
				break
			}
			onTcgWebOwnerOutRoom(data.data.rid)
			break;
		case 103:
			// 103 - 收到退出房间[玩家]消息  用不到该通知,暂时保留
			console.log("收到退出房间[玩家]消息")
			// if (data.platform == "tcgmini") {
			// 	break
			// }
			// onTcgWebPlayerOutRoom(data.data.rid)
			break;
		case 104:
			// 104 - 收到开始视频直播的消息
			console.log("收到开始视频直播的消息")
			if (data.platform == "tcgmini") {
				break
			}
			uni.$emit('onTcgWebOwnerStartFighting', data.data);
			break;
		case 105:
			// 105 - 收到开始视频直播的消息
			console.log("收到结束视频直播的消息")
			if (data.platform == "tcgmini") {
				break
			}
			uni.$emit('onTcgWebOwnerEndFighting', data.data);
			break;
		case 201:
			// 105 - 收到PC进入观战的消息
			console.log("收到PC进入观战的消息")
			if (data.platform == "tcgmini") {
				break
			}
			uni.$emit('onTcgWebEnterWatchLive');
			break;
		case 202:
			// 105 - 收到PC退出观战的消息
			console.log("收到PC退出观战的消息")
			if (data.platform == "tcgmini") {
				break
			}
			uni.$emit('onTcgWebEndWatchLive');
			break;
		default:
			break;
	}
}
// 处理群组系统消息
const onGroupSystemNoticeReceived = function (message : Message) {
	switch (message.payload.operationType) {
		case 4:
			// 5 - 被踢出群组
			// uni.$emit('onLeaveRoom', message.payload.groupProfile.groupID)
			console.log("被踢出群组");
			break;
		case 5:
			// 5 - 群组解散
			// uni.$emit('onRoomDismiss', message.payload.groupProfile.groupID)
			console.log("群组解散");
			break;
		case 255:
			// 255 - 自定义群系统通知
			const data = JSON.parse(message.payload.userDefinedField);
			if (data.type == 23) {
				// 23 - 更新房间信息
				uni.$emit('updateRoomInfo', message.payload.groupProfile.groupID)
			} else if (data.type == 20) {
				// 20 - 进入会议室
				uni.$emit('enterTVRoom')
			} else if (data.type == 22) {
				// 22 - 离开会议室
				uni.$emit('leaveTVRoom')
			} else if (data.type == 24) {
				// 24 - 更新房间玩家在线状态
				uni.$emit('updateRoomUserOnLineStatus', message)
			} else if (data.type == 25) {
				// 24 - 更新房间玩家是否准备游戏
				uni.$emit('updateRoomPlayerIsGameReady', data)
			}
			break;
		default:
			break;
	}
}

// 处理群组通知消息
const onGroupTipElemReceived = function (message : Message) {

	switch (message.payload.operationType) {
		case 1:
			console.log("用户进群啦", message.payload);
			// 5 - 有用户进群
			uni.$emit('onOtherUserEnterRoom', message.payload)
			break;
		case 3:
			// 5 - 有用户退群
			console.log("用户退群啦", message.payload);
			uni.$emit('onOtherUserLeaveRoom', message.payload)
			break;
		default:
			break;
	}
}

// SDK 收到推送的单聊、群聊、群提示、群系统通知的新消息，接入侧可通过遍历 event.data 获取消息列表数据并渲染到页面
let onMessageReceived = function (event : any) {
	// event.data - 存储 Message 对象的数组 - [Message]
	console.log("tcgIM监听收到新消息", event.data);
	let messages = event.data as Message[]
	for (let message of messages) {
		if (!empty(message.type)) {
			switch (message.type) {
				case TencentCloudChat.TYPES.MSG_CUSTOM:
					onCustomMessageReceived(message);
					break;
				case TencentCloudChat.TYPES.MSG_GRP_SYS_NOTICE:
					onGroupSystemNoticeReceived(message);
					break;
				case TencentCloudChat.TYPES.MSG_GRP_TIP:
					onGroupTipElemReceived(message);
					break;
				default:
					break;
			}
		}
	}
	// event.data.forEach(async (message : Message) => {
	// 	switch (message.type) {
	// 		case TencentCloudChat.TYPES.MSG_CUSTOM:
	// 			onCustomMessageReceived(message);
	// 			break;
	// 		case TencentCloudChat.TYPES.MSG_GRP_SYS_NOTICE:
	// 			onGroupSystemNoticeReceived(message);
	// 			break;
	// 		case TencentCloudChat.TYPES.MSG_GRP_TIP:
	// 			onGroupTipElemReceived(message);
	// 			break;
	// 		default:
	// 			break;
	// 	}
	// });
};
tcgChat.on(TencentCloudChat.EVENT.MESSAGE_RECEIVED, onMessageReceived);

// 会话未读总数更新，event.data 是当前单聊和群聊会话的未读总数。
let onTotalUnreadMessageCountUpdated = function (event : any) {
	// 当前单聊和群聊会话的未读总数
	console.log("tcgIM监听收到当前单聊和群聊会话的未读总数", event.data);
};
tcgChat.on(TencentCloudChat.EVENT.TOTAL_UNREAD_MESSAGE_COUNT_UPDATED, onTotalUnreadMessageCountUpdated);

// 会话列表更新，event.data 是包含 Conversation 对象的数组
let onConversationListUpdated = function (event : any) {
	console.log("tcgIM监听会话列表更新", event.data); // 包含 Conversation 实例的数组
	// Conversation 数据结构详情请参考 https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Conversation.html
};
tcgChat.on(TencentCloudChat.EVENT.CONVERSATION_LIST_UPDATED, onConversationListUpdated);

// 群属性更新时触发，接入侧可通过 event.data 获取到更新后的群属性数据。
let onGroupAttributesUpdated = function (event : any) {
	const groupID = event.data.groupID // 群组ID
	const groupAttributes = event.data.groupAttributes // 更新后的群属性
	console.log("tcgIM监听群属性更新", event.data);
};
tcgChat.on(TencentCloudChat.EVENT.GROUP_ATTRIBUTES_UPDATED, onGroupAttributesUpdated);

// 自己或好友的资料发生变更时触发，event.data 是包含 Profile 对象的数组
let onProfileUpdated = function (event : any) {
	console.log("tcgIM监听自己或好友的资料变更", event.data);
};
tcgChat.on(TencentCloudChat.EVENT.PROFILE_UPDATED, onProfileUpdated);

// 已订阅用户或好友的状态变更（在线状态或自定义状态）时触发。
let onUserStatusUpdated = function (event : any) {
	console.log("tcgIM监听好友在线状态变更", event.data);
	const userStatusList = event.data;
	userStatusList.forEach((item : any) => {
		const { userID, statusType, customStatus } = item;
		// userID - 用户 ID
		// statusType - 用户状态，枚举值及说明如下：
		// TencentCloudChat.TYPES.USER_STATUS_UNKNOWN - 未知
		// TencentCloudChat.TYPES.USER_STATUS_ONLINE - 在线
		// TencentCloudChat.TYPES.USER_STATUS_OFFLINE - 离线
		// TencentCloudChat.TYPES.USER_STATUS_UNLOGINED - 未登录
		// customStatus - 用户自定义状态
	})
};
tcgChat.on(TencentCloudChat.EVENT.USER_STATUS_UPDATED, onUserStatusUpdated);

// SDK 好友申请列表更新时触发。
let onFriendApplicationListUpdated = function (event : any) {
	// friendApplicationList - 好友申请列表 - [FriendApplication]
	// unreadCount - 好友申请的未读数
	const { friendApplicationList, unreadCount } = event.data;
	// 发送给我的好友申请（即别人申请加我为好友）
	const applicationSentToMe = friendApplicationList.filter((friendApplication : any) => {
		return friendApplication.type === TencentCloudChat.TYPES.SNS_APPLICATION_SENT_TO_ME
	});
	// 我发送出去的好友申请（即我申请加别人为好友）
	const applicationSentByMe = friendApplicationList.filter((friendApplication : any) => {
		return friendApplication.type === TencentCloudChat.TYPES.SNS_APPLICATION_SENT_BY_ME
	});
	console.log("tcgIM监听发给我的好友申请列表", applicationSentToMe);
};
tcgChat.on(TencentCloudChat.EVENT.FRIEND_APPLICATION_LIST_UPDATED, onFriendApplicationListUpdated);

// 用户被踢下线时触发。
let onKickedOut = function (event : any) {
	console.log("tcgIM监听用户被踢下线", event.data);
	console.log(event.data.type);
	if (event.data.type === TencentCloudChat.TYPES.KICKED_OUT_USERSIG_EXPIRED) {
		// 签名过期重新登录
		loginTcgWithLoginCode();
	}
	// TencentCloudChat.TYPES.KICKED_OUT_MULT_ACCOUNT(Web端，同一账号，多页面登录被踢)
	// TencentCloudChat.TYPES.KICKED_OUT_MULT_DEVICE(同一账号，多端登录被踢)
	// TencentCloudChat.TYPES.KICKED_OUT_USERSIG_EXPIRED(签名过期)
	// TencentCloudChat.TYPES.KICKED_OUT_REST_API(REST API kick 接口踢出)
};
tcgChat.on(TencentCloudChat.EVENT.KICKED_OUT, onKickedOut);
// // 群事件监听
// let onGroupListUpdated = function (event : any) {
// 	console.log("tcgIM监听群事件监听", event.data);// 包含 Group 实例的数组
// };
// tcgChat.on(TencentCloudChat.EVENT.GROUP_LIST_UPDATED, onGroupListUpdated);

export default tcgChat;